<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>fm.lizhi.ocean</groupId>
    <artifactId>web-ocean-seal</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>web-ocean-seal</name>
    <description>web-ocean-seal</description>

    <properties>
        <spring.mongodb.version>2.2.0.RELEASE</spring.mongodb.version>
        <mongo-java-driver.version>3.10.1</mongo-java-driver.version>
        <spring-boot.version>2.2.4.RELEASE</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <lz-common-dependencies-bom.version>1.4.25</lz-common-dependencies-bom.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-serviceclient</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>lz-commons-config</artifactId>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.commons</groupId>
            <artifactId>initiator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-access</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
        </dependency>
        <!-- 获取业务用户信息 -->
<!--        <dependency>-->
<!--            <groupId>fm.lizhi.pongpong.middle</groupId>-->
<!--            <artifactId>lz-pongpong-middle-user-api</artifactId>-->
<!--            <version>2.3.8-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>fm.lizhi.pp</groupId>-->
<!--            <artifactId>lz-pp-user-account-api</artifactId>-->
<!--            <version>1.2.5-SNAPSHOT</version>-->
<!--        </dependency>-->
        <!-- 获取业务用户信息 -->
        <dependency>
            <groupId>fm.lizhi.ocean</groupId>
            <artifactId>lz-ocean-seal-api</artifactId>
            <version>1.0.14-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>21.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.lizhi.pongpong</groupId>
            <artifactId>lz-pongpong-common-utils</artifactId>
            <version>2.3.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>kafka-client-spring-config</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>2.9.2</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.springframework.data</groupId>-->
<!--            <artifactId>spring-data-mongodb</artifactId>-->
<!--            <version>${spring.mongodb.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>mongo-java-driver</artifactId>-->
<!--                    <groupId>org.mongodb</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.mongodb</groupId>-->
<!--            <artifactId>mongo-java-driver</artifactId>-->
<!--            <version>${mongo-java-driver.version}</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.codehaus.jackson/jackson-mapper-asl -->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.codehaus.jackson/jackson-core-asl -->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
            <version>1.9.13</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.game</groupId>
            <artifactId>lz-game-auth-java</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>fm.lizhi.common</groupId>
            <artifactId>datastore-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.4.2.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.4.2.Final</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
            <version>5.7.11</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.16</version>
        </dependency>
        <!--luk游戏渠道-->
        <dependency>
            <groupId>io.github.cfgametech</groupId>
            <artifactId>luksdk</artifactId>
            <version>0.0.15</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>fm.lizhi.common</groupId>
                <artifactId>lz-common-dependencies-bom</artifactId>
                <version>${lz-common-dependencies-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <!--调整lombok的顺序，需要lombok先生成代码，再给mapstruct使用-->
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.18</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.4.2.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/assembly.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Central</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>codehaus-snapshots</id>
            <name>Codehaus Snapshots</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Plugin Repository</name>
            <url>http://maven.lizhi.fm:8081/nexus/content/groups/public/</url>
            <layout>default</layout>
            <snapshots>
            </snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>release</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://maven.lizhi.fm:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
