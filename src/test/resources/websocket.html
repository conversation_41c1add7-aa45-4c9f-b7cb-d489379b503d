<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebSocketTest</title>
</head>
<body>
用户状态：
<input id="state" name="state" value="0"/>

<script type="text/javascript">
    var websocket = new WebSocket("ws://localhost:8080/ws/123456789")

    /**
     * 心跳
     */
    function startHeartbeat() {
        setInterval(function () {
            const state = document.getElementById("state").value;
            websocket.send("{\"type\": 1, \"appId\": \"123\", \"token\": \"\", \"userId\": \"1577\", data: {\"userState\": " + state + "}}");
        }, 3000)
    }

    /**
     * 获取当前组用户信息
     */
    function getGroupUserInfos() {
        setInterval(function () {
            websocket.send("{\"type\": 6, \"appId\": \"123\", \"token\": \"\", \"userId\": \"1577\", data: {}}");
        }, 1000)
    }

    function join() {
        websocket.send("{\"type\": 2, \"appId\": \"123\", \"token\": \"\", \"userId\": \"1577\", data: {}}");
    }

    function cancelJoin() {
        websocket.send("{\"type\": 3, \"appId\": \"123\", \"token\": \"\", \"userId\": \"1577\", data: {}}");
    }

    function prepare() {
        websocket.send("{\"type\": 4, \"appId\": \"123\", \"token\": \"\", \"userId\": \"1577\", data: {}}");
    }

    function cancelPrepare() {
        websocket.send("{\"type\": 5, \"appId\": \"123\", \"token\": \"\", \"userId\": \"1577\", data: {}}");
    }

    websocket.onopen = function (event) {
        console.info("websocket connected");
        startHeartbeat();
        // 获取当前组信息
        getGroupUserInfos();
        // 加入游戏
        join();
        cancelJoin();
        prepare();
        cancelPrepare();
    }
    websocket.onclose = function (closeEvent) {
        console.info("on close, closeEvent = ", closeEvent);
    }
    websocket.onmessage = function (event) {
        console.info("on message, data = ", event.data);
    }
    websocket.onerror = function (event) {
        console.info("on error, params = ", event);
    }
</script>
</body>
</html>