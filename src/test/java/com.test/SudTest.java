package com.test;

import cn.hutool.crypto.digest.HMac;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;

import java.util.UUID;

/**
 * <AUTHOR>
 */
public class SudTest {

    public static void main(String[] args) {

        String appId = "1899410824146501632";
        String appSecret = "73TkRnpuQNYklT7EVpywlZxkFgeOpc79";


        // 请求时间戳（发送请求的时间戳）
        String timestamp = System.currentTimeMillis() + "";
        // 随机字符串 (自定义随机字符串)
        String nonce = UUID.randomUUID().toString();


        String body = "{\n" +
                "    \"event\": \"game_start\",\n" +
                "    \"mg_id\": \"1468434401847222273\",\n" +
                "    \"timestamp\": \"" + timestamp + "\",\n" +
                "    \"data\": {\n" +
                "        \"room_id\": \"12334123\",\n" +
                "        \"uid\": \"17576\",\n" +
                "        \"is_ready\": false\n" +
                "    }\n" +
                "}";

        // 签名串
        String signContent = String.format("%s\n%s\n%s\n%s\n", appId, timestamp, nonce, body);
        // 签名值
        HMac hMac = new HMac(HmacAlgorithm.HmacSHA1, appSecret.getBytes());
        String signature = hMac.digestHex(signContent);
        System.out.println(signature);


        HttpRequest post = HttpUtil.createPost("https://cn-000-mg-proxy.s01.tech/v1/app/server/push_event");
        post.header("Authorization", "Sud-Auth app_id=\""+appId+"\",nonce=\""+nonce+"\",timestamp=\""+timestamp+"\",signature=\""+signature+"\"");
        post.body(body);

        String resp = post.execute().body();

        System.out.println(resp);


    }
}
