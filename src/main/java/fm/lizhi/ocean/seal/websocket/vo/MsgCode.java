package fm.lizhi.ocean.seal.websocket.vo;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-05-05 15:31.
 *
 * <AUTHOR>
 */
public enum MsgCode {
    //
    SUCCESS(0, "成功"),
    ERROR_TOKEN(1, "token已失效"),
    ERROR_PARAMS(2, "参数错误"),
    ERROR_MSG_TYPE(3, "消息类型不存在"),
    ERROR_USER_COUNT(4, "游戏人数不符合要求"),
    ERROR_USER_CAPTAIN(5, "需要设置host才可开始游戏"),
    ERROR_SET_SEAT(6, "上座失败"),
    ERROR_USER_NOT_CAPTAIN(7, "当前用户不是队长"),
    ERROR_PLAYER_FULL(8, "玩家人数已满"),
    ERROR_JOIN_GAME(9, "加入游戏不成功"),
    ERROR_STATE_CHANGE(10, "非法状态变更"),

    ERROR_EXCEPTION(400, "操作失败"),
    ERROR(500, "未知错误");
    private int code;
    private String msg;

    private static Map<Integer, MsgCode> map = new HashMap<>();

    static {
        for (MsgCode object : MsgCode.values()) {
            map.put(object.getCode(), object);
        }
    }

    MsgCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据枚举代码找枚举
     *
     * @param code 值
     * @return
     */
    public static MsgCode from(int code) {
        return map.get(code);
    }
}