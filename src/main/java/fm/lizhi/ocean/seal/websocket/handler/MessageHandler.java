//package fm.lizhi.ocean.seal.websocket.handler;
//
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//
//import javax.websocket.Session;
//
///**
// * WebSocket消息处理接口
// * <p>
// * Created in 2022-05-05 16:55.
// *
// * <AUTHOR>
// */
//public interface MessageHandler {
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    public MsgType acceptType();
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    public ResponseVo<?> handler(Session session, Message message);
//}
