//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.param.SetUserInfoParam;
//import fm.lizhi.ocean.seal.websocket.service.BroadCastService;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//
///**
// * Created in 2022-05-23 17:37.
// *
// * <AUTHOR>
// */
//@Service
//public class SetUserInfoMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(SetUserInfoMessageHandler.class);
//    @Resource
//    private BroadCastService broadCastService;
//    @Resource
//    private WsUserService wsUserService;
//    @Resource
//    private LzConfig lzConfig;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.GAME_SET_USER_INFO;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        SetUserInfoParam param = JSON.parseObject(message.getData(), SetUserInfoParam.class);
//        String groupId = SessionUtils.getGroupId(session);
//        long userId = message.getUserId();
//        if (lzConfig.isWebSocketDebugLog()) {
//            logger.info("Set user info, groupId:{}, userId:{}, params:{}", groupId, userId, param);
//        }
//        // 更新角色
//        this.wsUserService.updateUserRole(groupId, userId, param.getRole());
//        this.wsUserService.updateUserInfo(groupId, userId, param.getNickName(), param.getGender(), param.getPortrait());
//        this.broadCastService.broadcastUserStateChange(groupId);
//        return ResponseVo.success();
//    }
//}