package fm.lizhi.ocean.seal.websocket.utils;

import fm.lizhi.common.datastore.redis.client.RedisClient;
import fm.lizhi.ocean.seal.constant.RedisNamespace;
import fm.lizhi.ocean.seal.websocket.constants.RedisKeys;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import redis.clients.jedis.params.SetParams;
import sun.nio.cs.ext.TIS_620;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * 基于Redis的分布式锁
 * <p>
 * Created in 2022-05-24 12:23.
 *
 * <AUTHOR>
 */
@Component
public class RedisLock {
    private static final Logger logger = LoggerFactory.getLogger(RedisLock.class);
    @Resource(name = RedisNamespace.REDIS_OCEAN_SEAL)
    private RedisClient redisClient;

    /**
     * 获取锁
     *
     * @param name      锁名称
     * @param owner     所有者
     * @param expireMs    锁过期时间，毫秒
     * @param timeoutMs 获取锁超时时间，毫秒
     * @return
     */
    public boolean lock(String name, String owner, int expireMs, int timeoutMs) {
        String key = RedisKeys.WEBSOCKET_LOCK.buildKey(name);
        long endTime = System.currentTimeMillis() + timeoutMs;
        while (System.currentTimeMillis() < endTime) {
            SetParams params = SetParams.setParams().nx().px(expireMs);
            String result = this.redisClient.set(key, owner, params);
            if ("OK".equals(result)) {
                logger.info("Lock resource success, key:{}, owner:{}, expireMs:{}, timeoutMs:{}", key, owner, expireMs, timeoutMs);
                return true;
            }
            // 休眠50毫秒
            LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(50));
        }
        logger.info("Lock resource failed, key:{}, owner:{}, expireMs:{}, timeoutMs:{}", key, owner, expireMs, timeoutMs);
        return false;
    }

    /**
     * 释放锁
     *
     * @param name  锁名称
     * @param owner 所有者
     * @return
     */
    public boolean unlock(String name, String owner) {
        String key = RedisKeys.WEBSOCKET_LOCK.buildKey(name);
        boolean unlockResult = false;
        String result = this.redisClient.get(key);
        if (StringUtils.isBlank(result)) {
            unlockResult = true;
            this.redisClient.del(name);
        } else if (owner.equals(result)) {
            this.redisClient.del(key);
            unlockResult = true;
        }
        logger.info("unlock resource, name:{}, owner:{}, result:{}", owner, name, unlockResult);
        return unlockResult;
    }
}