package fm.lizhi.ocean.seal.websocket.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-11 17:58.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class GameConfig {
    /**
     * 场景
     *
     * @see fm.lizhi.ocean.seal.websocket.constants.GameScene
     */
    private int scene;
    /**
     * 最小人数，依据场景而定
     */
    private int minPlayer;
    /**
     * 最大人数，依据场景而定
     */
    private int maxPlayer;
    /**
     * 等待页URL
     */
    private String waitUrl;
    /**
     * 是否需要队长，1：需要，0：不需要
     */
    private int captain;
}