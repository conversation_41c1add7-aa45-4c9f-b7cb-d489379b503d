//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.ocean.seal.service.GameFacadeService;
//import fm.lizhi.ocean.seal.websocket.constants.GameScene;
//import fm.lizhi.ocean.seal.websocket.constants.GameState;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.dto.BizResult;
//import fm.lizhi.ocean.seal.websocket.dto.GameConfig;
//import fm.lizhi.ocean.seal.websocket.dto.User;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.param.StartGameParam;
//import fm.lizhi.ocean.seal.websocket.service.BroadCastService;
//import fm.lizhi.ocean.seal.websocket.service.WsGameService;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//import java.util.List;
//
///**
// * 加载游戏页
// * <p>
// * Created in 2022-05-09 10:36.
// *
// * <AUTHOR>
// */
//@Service
//public class StartGameMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(StartGameMessageHandler.class);
//    @Resource
//    private BroadCastService broadCastService;
//    @Resource
//    private GameFacadeService gameFacadeService;
//    @Resource
//    private WsUserService wsUserService;
//    @Resource
//    private WsGameService wsGameService;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.START_GAME;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        StartGameParam param = JSON.parseObject(message.getData(), StartGameParam.class);
//        String groupId = SessionUtils.getGroupId(session);
//        long userId = message.getUserId();
//        if (StringUtils.isEmpty(param.getGameId())) {
//            return ResponseVo.msgCode(MsgCode.ERROR_PARAMS);
//        }
//        BizResult<GameConfig> result = this.gameFacadeService.getGameConfig(message.getAppId(), param.getGameId());
//        if (result.isNotSuccess()) {
//            return ResponseVo.msgCode(result.getCode(), result.getMsg());
//        }
//        GameConfig config = result.getData();
//        // 如果需要队长，但没有，则提示不能开始游戏
//        if (config.getCaptain() == 1 && !this.checkCaptain(groupId)) {
//            logger.error("Failed to start the game, captain error, groupId:{}, param:{}", groupId, param);
//            return ResponseVo.msgCode(MsgCode.ERROR_USER_CAPTAIN);
//        }
//
//        // 如果当前用户不是队长，则不能开始游戏
//        User user = this.wsUserService.getUser(groupId, userId);
//        if (config.getCaptain() == 1 && user.getUserId() != userId) {
//            logger.error("Failed to start the game, the current user is not the captain, groupId:{}, param:{}, user:{}", groupId, param, user);
//            return ResponseVo.msgCode(MsgCode.ERROR_USER_NOT_CAPTAIN);
//        }
//
//        int count = 0;
//        // 如果只是加入场景，那么可以取加入状态和准备状态之和，否则只取准备状态
//        if (config.getScene() == GameScene.JOIN.getValue()) {
//            count = this.wsUserService.getUserCount(groupId, UserState.JOINED_NOT_READY, UserState.READY);
//        } else {
//            count = this.wsUserService.getUserCount(groupId, UserState.READY);
//        }
//        if (count >= config.getMinPlayer() && count <= config.getMaxPlayer()) {
//            this.wsUserService.updateUserState(groupId, userId, UserState.LOAD_GAME_PAGE);
//            this.wsUserService.updateUserState(groupId, UserState.LOAD_GAME_PAGE);
//            this.wsGameService.updateGameState(groupId, GameState.GAME_PAGE);
//            this.broadCastService.broadcastStartGame(groupId);
//            return ResponseVo.success();
//        } else {
//            logger.error("Failed to start the game, illegal number of players, groupId:{}, params:{}, count:{}, config:{}", groupId, param, count, config);
//            return ResponseVo.msgCode(MsgCode.ERROR_USER_COUNT);
//        }
//    }
//
//    /**
//     * 检查队长是否合法
//     *
//     * @param groupId 组ID
//     * @return
//     */
//    private boolean checkCaptain(String groupId) {
//        List<User> users = this.wsUserService.getGroupUsers(groupId);
//        int captainCount = 0;
//        for (User user : users) {
//            if (user.getRole() == 1) {
//                captainCount ++;
//            }
//        }
//        if (captainCount != 1) {
//            logger.error("Captain check failed, groupId:{}, captainCount:{}, users:{}", groupId, captainCount, users);
//            return false;
//        }
//        return true;
//    }
//}