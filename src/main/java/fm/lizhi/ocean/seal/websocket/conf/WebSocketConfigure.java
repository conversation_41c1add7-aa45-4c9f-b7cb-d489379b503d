//package fm.lizhi.ocean.seal.websocket.conf;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import javax.websocket.HandshakeResponse;
//import javax.websocket.server.HandshakeRequest;
//import javax.websocket.server.ServerEndpointConfig;
//import java.util.List;
//import java.util.Map;
//
//import static fm.lizhi.ocean.seal.websocket.utils.WebSocketRouteUtils.REAL_USER_IP_ATTR;
//
///**
// * Created in 2022-05-07 09:56.
// *
// * <AUTHOR>
// */
//public class WebSocketConfigure extends ServerEndpointConfig.Configurator{
//    private static final Logger logger = LoggerFactory.getLogger(WebSocketConfigure.class);
//
//    @Override
//    public void modifyHandshake(ServerEndpointConfig config, HandshakeRequest request, HandshakeResponse response) {
//        super.modifyHandshake(config, request, response);
//        try {
//            Map<String, List<String>> headers = request.getHeaders();
//            String userIp = "";
//            if (headers.containsKey("x-real-ip")) {
//                userIp = headers.get("x-real-ip").get(0);
//            }
//            if (!"".equals(userIp) && userIp != null) {
//                config.getUserProperties().put(REAL_USER_IP_ATTR, userIp);
//            }
//        } catch (Throwable t) {
//        }
//    }
//}