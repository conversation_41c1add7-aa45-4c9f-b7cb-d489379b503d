package fm.lizhi.ocean.seal.websocket.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-05-05 16:50.
 *
 * <AUTHOR>
 */
public enum MsgType {
    //
    EVENT_PUSH(0, "事件推送"),
    HEARTBEAT(1, "用户心跳"),
    JOIN(2, "加入游戏"),
    CANCEL_JOIN(3, "取消加入"),
    PREPARE(4, "准备游戏"),
    CANCEL_PREPARE(5, "取消准备"),
    GET_GROUP_INFO(6, "获取当前组信息"),
    START_GAME(7, "开始游戏"),
    GAME_SET_USER_INFO(8, "设置用户信息"),

    ;
    private int code;
    private String msg;

    private static Map<Integer, MsgType> map = new HashMap<>();

    static {
        for (MsgType object : MsgType.values()) {
            map.put(object.getCode(), object);
        }
    }

    MsgType(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据枚举代码找枚举
     *
     * @param code 值
     * @return
     */
    public static MsgType from(int code) {
        return map.get(code);
    }
}