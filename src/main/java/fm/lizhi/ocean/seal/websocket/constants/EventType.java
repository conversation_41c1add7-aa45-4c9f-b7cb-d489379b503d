package fm.lizhi.ocean.seal.websocket.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 广播事件类型
 *
 * Created in 2022-05-06 15:59.
 *
 * <AUTHOR>
 */
public enum EventType {
    //
    USER_STATE_CHANGE(1, "用户状态变更"),
    START_GAME(2, "开始游戏"),
    ;
    private int code;
    private String msg;

    private static Map<Integer, EventType> map = new HashMap<>();

    static {
        for (EventType object : EventType.values()) {
            map.put(object.getCode(), object);
        }
    }

    EventType(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据枚举代码找枚举
     *
     * @param code 值
     * @return
     */
    public static EventType from(int code) {
        return map.get(code);
    }
}