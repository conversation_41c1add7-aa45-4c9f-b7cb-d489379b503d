//package fm.lizhi.ocean.seal.websocket.service;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.common.datastore.redis.client.RedisClient;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.constant.RedisNamespace;
//import fm.lizhi.ocean.seal.websocket.constants.GameState;
//import fm.lizhi.ocean.seal.websocket.constants.RedisKeys;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.vo.GameInfo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
///**
// * 等待页中，游戏部分逻辑
// * <p>
// * Created in 2022-05-27 09:58.
// *
// * <AUTHOR>
// */
//@Service
//public class WsGameService {
//    private static final Logger logger = LoggerFactory.getLogger(WsGameService.class);
//    @Resource(name = RedisNamespace.REDIS_OCEAN_SEAL)
//    private RedisClient redisClient;
//    @Resource
//    private LzConfig lzConfig;
//
//    /**
//     * 获取游戏相关信息
//     *
//     * @param groupId
//     * @return
//     */
//    public GameInfo getGameInfo(String groupId) {
//        GameInfo gameInfo = new GameInfo();
//        String key = RedisKeys.WEBSOCKET_GROUP_INFO.buildKey(groupId);
//        Map<String, String> result = this.redisClient.hgetAll(key);
//        if (result != null) {
//            String json = JSON.toJSONString(result);
//            gameInfo = JSON.parseObject(json, GameInfo.class);
//        }
//        return gameInfo;
//    }
//
//    /**
//     * 更新游戏状态
//     *
//     * @param groupId 组ID
//     * @param state 状态
//     */
//    public void updateGameState(String groupId, GameState state) {
//        String key = RedisKeys.WEBSOCKET_GROUP_INFO.buildKey(groupId);
//        this.redisClient.hset(key, "state", String.valueOf(state.getValue()));
//        this.redisClient.expire(key, this.lzConfig.getGameStateExpireTime());
//    }
//}