package fm.lizhi.ocean.seal.websocket.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 等待页用户状态
 * <p>
 * Created in 2022-05-06 11:23.
 *
 * <AUTHOR>
 */
public enum UserState {
    //
    IDLE(0, "空闲状态"),
    JOINED_NOT_READY(1, "已加入未准备"),
    READY(2, "已准备"),
    LOAD_GAME_PAGE(3, "加载游戏页"),

    ;
    private int code;
    private String msg;

    private static Map<Integer, UserState> map = new HashMap<>();

    static {
        for (UserState object : UserState.values()) {
            map.put(object.getCode(), object);
        }
    }

    UserState(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据枚举代码找枚举
     *
     * @param code 值
     * @return
     */
    public static UserState from(int code) {
        return map.get(code);
    }
}