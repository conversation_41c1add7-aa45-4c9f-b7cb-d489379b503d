//package fm.lizhi.ocean.seal.websocket.service;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.common.datastore.redis.client.RedisClient;
//import fm.lizhi.ocean.seal.constant.RedisNamespace;
//import fm.lizhi.ocean.seal.websocket.constants.RedisKeys;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.dto.User;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Random;
//import java.util.concurrent.TimeUnit;
//
///**
// * 等待页中，用户相关逻辑
// * <p>
// * Created in 2022-05-27 09:58.
// *
// * <AUTHOR>
// */
//@Service
//public class WsUserService {
//    private static final Logger logger = LoggerFactory.getLogger(WsUserService.class);
//    private final Random random = new Random();
//    @Resource(name = RedisNamespace.REDIS_OCEAN_SEAL)
//    private RedisClient redisClient;
//    @Resource
//    private BroadCastService broadCastService;
//
//    /**
//     * 更新用户状态
//     *
//     * @param groupId   组ID
//     * @param userId    用户ID
//     * @param userState 用户状态
//     * @return false： 非法状态变更，变更失败；true：变更成功
//     */
//    public boolean updateUserState(String groupId, long userId, UserState userState) {
//        boolean syncClientState = false;
//        User user = this.getUser(groupId, userId);
//        // 获取原先的状态，如果原先没有则为null
//        UserState preState = user != null ? UserState.from(user.getState()) : null;
//        if (user == null) {
//            user = this.initUser(userId, userState);
//        }
//
//        // 检测状态是否非法变更
//        //1. 空闲-->已准备
//        //2. 已准备-->空闲
//        if (preState == UserState.IDLE && userState == UserState.READY) {
//            logger.info("Failed to update user state, illegal state change, groupId:{}, userId:{}, preState:{}, newState:{}", groupId, userId, preState, userState);
//            return false;
//        }
//        if (preState == UserState.READY && userState == UserState.IDLE) {
//            logger.info("Failed to update user state, illegal state change, groupId:{}, userId:{}, preState:{}, newState:{}", groupId, userId, preState, userState);
//            return false;
//        }
//
//        // 设置用户状态和最新活跃时间
//        user.setLastActiveTime(System.currentTimeMillis());
//        if (user.getSeat() <= 0 && (userState.getCode() == UserState.JOINED_NOT_READY.getCode() || userState.getCode() == UserState.READY.getCode())) {
//            user.setState(UserState.IDLE.getCode());
//            syncClientState = true;
//        } else {
//            user.setState(userState.getCode());
//        }
//        this.updateUser(groupId, user);
//        if (preState == null || preState.getCode() != userState.getCode() || syncClientState) {
//            this.broadCastService.broadcastUserStateChange(groupId);
//        }
//        return true;
//    }
//
//    /**
//     * 更新用户信息
//     *
//     * @param groupId 组ID
//     * @param user    用户
//     */
//    public void updateUser(String groupId, User user) {
//        String userJson = JSON.toJSONString(user);
//        String key = RedisKeys.WEBSOCKET_GROUP.buildKey(groupId);
//        int expire = (int) (TimeUnit.MINUTES.toSeconds(10) + this.random.nextInt(30));
//        this.redisClient.hset(key, String.valueOf(user.getUserId()), userJson);
//        this.redisClient.expire(key, expire);
//        logger.info("Update user, key:{}, groupId:{}, user:{}", key, groupId, userJson);
//    }
//
//    /**
//     * 初始化用户信息
//     *
//     * @param userId    用户ID
//     * @param userState 用户状态
//     * @return
//     */
//    private User initUser(long userId, UserState userState) {
//        User user = new User();
//        user.setUserId(userId);
//        user.setState(userState.getCode());
//        user.setLastActiveTime(System.currentTimeMillis());
//        user.setSeat(0);
//        user.setRole(-1);
//        user.setNickName("");
//        user.setGender(0);
//        user.setPortrait("");
//        return user;
//    }
//
//    /**
//     * 从Redis中获取用户信息
//     *
//     * @param groupId 组ID
//     * @param userId  用户ID
//     * @return
//     */
//    public User getUser(String groupId, long userId) {
//        String key = RedisKeys.WEBSOCKET_GROUP.buildKey(groupId);
//        String json = this.redisClient.hget(key, String.valueOf(userId));
//        if (StringUtils.isNotEmpty(json)) {
//            return JSON.parseObject(json, User.class);
//        }
//        return null;
//    }
//
//    /**
//     * 获取当前组所有用户信息
//     *
//     * @param groupId 组Id
//     * @return
//     */
//    public List<User> getGroupUsers(String groupId) {
//        List<User> users = new ArrayList<>();
//        String key = RedisKeys.WEBSOCKET_GROUP.buildKey(groupId);
//        Map<String, String> map = this.redisClient.hgetAll(key);
//        for (String userJson : map.values()) {
//            User user = JSON.parseObject(userJson, User.class);
//            users.add(user);
//        }
//        return users;
//    }
//
//    /**
//     * 更新用户角色
//     *
//     * @param groupId 组ID
//     * @param userId  用户ID
//     * @param role    角色
//     */
//    public void updateUserRole(String groupId, long userId, int role) {
//        // 设置队长时，取消其他队长身份
//        if (role == 1) {
//            List<User> users = this.getGroupUsers(groupId);
//            for (User user : users) {
//                if (user.getUserId() != userId && user.getRole() == 1) {
//                    // 取消该用户的队长身份
//                    user.setRole(0);
//                    this.updateUser(groupId, user);
//                }
//            }
//        }
//        User user = this.getUser(groupId, userId);
//        user.setRole(role);
//        this.updateUser(groupId, user);
//    }
//
//    /**
//     * 更新用户信息
//     *
//     * @param groupId  组ID
//     * @param userId   用户ID
//     * @param nickName 昵称
//     * @param gender   性别
//     * @param portrait 头像
//     */
//    public void updateUserInfo(String groupId, long userId, String nickName, int gender, String portrait) {
//        User user = this.getUser(groupId, userId);
//        if (user != null) {
//            user.setNickName(nickName);
//            user.setGender(gender);
//            user.setPortrait(portrait);
//            this.updateUser(groupId, user);
//        }
//    }
//
//    /**
//     * 获取用户人数
//     *
//     * @param groupId    组ID
//     * @param userStates 用户状态
//     * @return
//     */
//    public int getUserCount(String groupId, UserState... userStates) {
//        int count = 0;
//        List<User> users = this.getGroupUsers(groupId);
//        for (User user : users) {
//            for (UserState userState : userStates) {
//                if (user.getState() == userState.getCode()) {
//                    count++;
//                }
//            }
//        }
//        return count;
//    }
//
//    /**
//     * 更新所有用户状态
//     *
//     * @param groupId   组ID
//     * @param userState 用户状态
//     */
//    public void updateUserState(String groupId, UserState userState) {
//        List<User> users = this.getGroupUsers(groupId);
//        for (User user : users) {
//            user.setState(userState.getCode());
//            this.updateUser(groupId, user);
//        }
//    }
//
//    /**
//     * 获取玩家人数
//     *
//     * @param groupId 组ID
//     * @return
//     */
//    public int getPlayerCount(String groupId) {
//        int count = 0;
//        List<User> users = this.getGroupUsers(groupId);
//        for (User user : users) {
//            if (user.getState() == UserState.JOINED_NOT_READY.getCode()
//                    || user.getState() == UserState.READY.getCode()
//                    || user.getState() == UserState.LOAD_GAME_PAGE.getCode()) {
//                count++;
//            }
//        }
//        return count;
//    }
//}