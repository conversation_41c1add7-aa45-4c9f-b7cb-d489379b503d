package fm.lizhi.ocean.seal.websocket.dto;

import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
import lombok.Data;

/**
 * Created in 2022-04-28 16:52.
 *
 * <AUTHOR>
 */
@Data
public class BizResult<T> {
    private int code;
    private String msg;
    private T data;

    public BizResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public BizResult(MsgCode msgCode) {
        this.code = msgCode.getCode();
        this.msg = msgCode.getMsg();
    }

    public static <T> BizResult<T> success() {
        return new BizResult<T>(MsgCode.SUCCESS);
    }

    public boolean isSuccess() {
        return this.code == MsgCode.SUCCESS.getCode();
    }

    public boolean isNotSuccess() {
        return !this.isSuccess();
    }
}