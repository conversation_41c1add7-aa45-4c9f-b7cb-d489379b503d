//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.param.HeartbeatParam;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//
///**
// * 用户心跳
// * <p>
// * Created in 2022-05-05 17:27.
// *
// * <AUTHOR>
// */
//@Service
//public class HeartBeatMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(HeartBeatMessageHandler.class);
//    @Resource
//    private LzConfig lzConfig;
//    @Resource
//    private WsUserService wsUserService;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.HEARTBEAT;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 上下文
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        String groupId = SessionUtils.getGroupId(session);
//        long userId = message.getUserId();
//        HeartbeatParam param = JSON.parseObject(message.getData(), HeartbeatParam.class);
//        UserState userState = UserState.from(param.getUserState());
//        if (message.getUserId() <= 0) {
//            logger.warn("User heartbeat failed, userId is invalid, groupId:{}, message:{}", groupId, message);
//            return ResponseVo.msgCode(MsgCode.ERROR_PARAMS);
//        }
//        if (userState == null) {
//            logger.warn("User heartbeat failed, user state is invalid, groupId:{}, message:{}", groupId, message);
//            return ResponseVo.msgCode(MsgCode.ERROR_PARAMS);
//        }
//        if (this.lzConfig.isWebSocketDebugLog()) {
//            logger.info("User heartbeat, groupId:{}, message:{}", groupId, message);
//        }
//        boolean success = this.wsUserService.updateUserState(groupId, userId, userState);
//        if (!success) {
//            return ResponseVo.msgCode(MsgCode.ERROR_STATE_CHANGE);
//        }
//        return ResponseVo.success();
//    }
//}