package fm.lizhi.ocean.seal.websocket.utils;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.websocket.Session;

/**
 * Created in 2022-05-07 10:44.
 *
 * <AUTHOR>
 */
public class ResponseUtils {
    private static final Logger logger = LoggerFactory.getLogger(ResponseUtils.class);

    /**
     * 发送响应
     *
     * @param session 会话
     * @param type    消息类型
     * @param seq     请求标识
     * @param msgCode 状态码
     */
    public static void sendResponse(Session session, int type, String seq, MsgCode msgCode) {
        ResponseVo<?> responseVo = ResponseVo.msgCode(msgCode);
        responseVo.setType(type);
        responseVo.setSeq(seq);
        sendResponse(session, responseVo);
    }

    /**
     * 发送响应
     *
     * @param session    会话
     * @param responseVo 状态码
     */
    public static void sendResponse(Session session, ResponseVo<?> responseVo) {
        // 同一个Session下，避免并发的向同个客户端写数据
        synchronized (session) {
            if (session.isOpen()) {
                String data = JSON.toJSONString(responseVo);
                try {
                    session.getBasicRemote().sendText(data);
                } catch (Exception e) {
                    logger.error("Failed to send message to client, userProperties:{}, response:{}, msg:{}", session.getUserProperties(), responseVo, e.getMessage(), e);
                }
            } else {
                logger.error("Failed to send message, session is close, response:{}", responseVo);
            }
        }
    }
}