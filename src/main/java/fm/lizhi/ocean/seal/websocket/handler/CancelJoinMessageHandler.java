//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.dto.User;
//import fm.lizhi.ocean.seal.websocket.param.CancelJoinParam;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//
///**
// * 取消加入
// * <p>
// * Created in 2022-05-09 10:36.
// *
// * <AUTHOR>
// */
//@Service
//public class CancelJoinMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(CancelJoinMessageHandler.class);
//    @Resource
//    private WsUserService wsUserService;
//    @Resource
//    private LzConfig lzConfig;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.CANCEL_JOIN;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        String groupId = SessionUtils.getGroupId(session);
//        long userId = message.getUserId();
//        CancelJoinParam param = JSON.parseObject(message.getData(), CancelJoinParam.class);
//        if (this.lzConfig.isWebSocketDebugLog()) {
//            logger.info("Cancel join, groupId:{}, userId:{}, param:{}", groupId, userId, param);
//        }
//        boolean success = this.wsUserService.updateUserState(groupId, userId, UserState.IDLE);
//        if (!success) {
//            return ResponseVo.msgCode(MsgCode.ERROR_STATE_CHANGE);
//        }
//        this.cancelJoin(groupId, userId);
//        return ResponseVo.success();
//    }
//
//    /**
//     * 取消加入
//     *
//     * @param groupId 组ID
//     * @param userId  用户ID
//     */
//    private void cancelJoin(String groupId, long userId) {
//        User user = this.wsUserService.getUser(groupId, userId);
//        user.setSeat(0);
//        this.wsUserService.updateUser(groupId, user);
//    }
//}