package fm.lizhi.ocean.seal.websocket.utils;

import javax.websocket.Session;

/**
 * Created in 2022-05-09 10:59.
 *
 * <AUTHOR>
 */
public class SessionUtils {
    private static final String USERID = "user-id";

    /**
     * 获取组ID
     *
     * @param session 会话
     * @return
     */
    public static String getGroupId(Session session) {
        return (String) session.getUserProperties().get("groupId");
    }

    /**
     * 获取用户IP
     *
     * @param session 会话
     * @return
     */
    public static String getUserIp(Session session) {
        if (session.isOpen()) {
            if (session.getUserProperties().containsKey(WebSocketRouteUtils.REAL_USER_IP_ATTR)) {
                return (String) session.getUserProperties().get(WebSocketRouteUtils.REAL_USER_IP_ATTR);
            }
        }
        return "";
    }

    /**
     * 获取用户ID
     *
     * @param session 会话
     * @return
     */
    public static String getUserId(Session session) {
        return (String) session.getUserProperties().get(USERID);
    }
}