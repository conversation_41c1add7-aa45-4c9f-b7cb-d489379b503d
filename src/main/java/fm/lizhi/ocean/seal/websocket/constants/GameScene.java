package fm.lizhi.ocean.seal.websocket.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-05-16 11:02.
 *
 * <AUTHOR>
 */
public enum GameScene {
    /**
     * 有加入游戏
     */
    JOIN(1, "加入游戏"),
    /**
     * 有准备游戏
     */
    PREPARE(2, "准备游戏"),
    /**
     * 有开始游戏
     */
    START_GAME(3, "开始游戏"),

    ;
    private int value;
    private String msg;

    private static Map<Integer, GameScene> map = new HashMap<>();

    static {
        for (GameScene object : GameScene.values()) {
            map.put(object.getValue(), object);
        }
    }

    GameScene(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static GameScene from(int value) {
        return map.get(value);
    }
}