package fm.lizhi.ocean.seal.websocket.constants;

import org.apache.commons.lang3.StringUtils;

/**
 * Created in 2022-05-06 14:18.
 *
 * <AUTHOR>
 */
public enum RedisKeys {
    /**
     * 连接组
     */
    WEBSOCKET_GROUP,
    /**
     * 事件通道
     */
    WEBSOCKET_EVENT_CHANNEL,
    /**
     * 分布式锁
     */
    WEBSOCKET_LOCK,
    /**
     * 组信息
     */
    WEBSOCKET_GROUP_INFO,
    ;

    /**
     * 构建Key
     *
     * @param param 参数
     * @return
     */
    public String buildKey(String param) {
        if (StringUtils.isNotBlank(param)) {
            param = "_" + param;
        } else {
            param = "";
        }
        return "WEB_OCEAN_SEAL_" + this.name() + param;
    }
}
