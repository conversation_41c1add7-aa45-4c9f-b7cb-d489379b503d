package fm.lizhi.ocean.seal.websocket.param;

import lombok.Data;
import lombok.ToString;

/**
 * WebSocket消息结构
 * <p>
 * Created in 2022-04-29 15:30.
 *
 * <AUTHOR>
 */
@Data
@ToString
public class Message {
    /**
     * 消息类型
     *
     * @see fm.lizhi.ocean.seal.websocket.constants.MsgType
     */
    private int type;
    /**
     * 标识一个唯一的请求，前端生成，服务端回传
     */
    private String seq;
    /**
     * 业务方appId
     */
    private String appId;
    /**
     * 用户ID
     */
    private long userId;
    /**
     * seal token
     */
    private String token;
    /**
     * 请求数据
     */
    private String data;
}