// FIXME 注释了webSocket的逻辑

//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.commons.service.client.pojo.Result;
//import fm.lizhi.ocean.seal.api.SealTokenService;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto;
//import fm.lizhi.ocean.seal.websocket.conf.WebSocketConfigure;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.service.ConnectionService;
//import fm.lizhi.ocean.seal.websocket.service.MessageHandlerService;
//import fm.lizhi.ocean.seal.websocket.utils.ResponseUtils;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.utils.WebSocketRouteUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import javax.websocket.*;
//import javax.websocket.server.PathParam;
//import javax.websocket.server.ServerEndpoint;
//
///**
// * 这不是一个单例，每个新连接都会创建一个对象
// * <p>
// * Created in 2022-05-07 09:49.
// *
// * <AUTHOR>
// */
//@Component
//@ServerEndpoint(value = "/ws/{groupId}", configurator = WebSocketConfigure.class)
//public class WebSocketServer {
//    private static final Logger logger = LoggerFactory.getLogger(WebSocketServer.class);
//    private static LzConfig lzConfig;
//    private static SealTokenService sealTokenService;
//    private static MessageHandlerService messageHandlerService;
//    private static ConnectionService connectionService;
//
//    @Resource
//    public void setLzConfig(LzConfig lzConfig) {
//        WebSocketServer.lzConfig = lzConfig;
//    }
//
//    @Resource
//    public void setSealTokenService(SealTokenService sealTokenService) {
//        WebSocketServer.sealTokenService = sealTokenService;
//    }
//
//    @Resource
//    public void setMessageHandlerService(MessageHandlerService messageHandlerService) {
//        WebSocketServer.messageHandlerService = messageHandlerService;
//    }
//
//    @Resource
//    public void setConnectionService(ConnectionService connectionService) {
//        WebSocketServer.connectionService = connectionService;
//    }
//
//    /**
//     * 建立连接时
//     *
//     * @param session 会话
//     * @param groupId 连接组ID
//     * @param config  配置
//     */
//    @OnOpen
//    public void onOpen(Session session, EndpointConfig config, @PathParam("groupId") String groupId) {
//        WebSocketRouteUtils.setRealUserIpToSession(session, config);
//        session.getUserProperties().put("groupId", groupId);
//        logger.info("on open, groupId:{}, userIP:{}", groupId, SessionUtils.getUserIp(session));
//    }
//
//    /**
//     * 收到消息时
//     *
//     * @param session 会话
//     * @param msg     消息体
//     */
//    @OnMessage
//    public void onMessage(Session session, String msg) {
//        String groupId = SessionUtils.getGroupId(session);
//        long startTime = System.currentTimeMillis();
//        WebSocketRouteUtils.setRealUserIpToContainer(session);
//        Message message = JSON.parseObject(msg, Message.class);
//        if (lzConfig.isWebSocketDebugLog()) {
//            logger.info("Received websocket message, groupId:{}, message:{}", groupId, message);
//        }
//        if (lzConfig.isWebSocketTokenVerify()) {
//            // token有效性校验
//            boolean tokenValid = this.tokenVerify(message.getAppId(), message.getToken());
//            if (!tokenValid) {
//                logger.warn("Token has expired, groupId:{}, message:{}", groupId, message);
//                ResponseUtils.sendResponse(session, message.getType(), message.getSeq(), MsgCode.ERROR_TOKEN);
//                return;
//            }
//        }
//        // 更新用户最后活跃时间
//        connectionService.updateLastActiveTime(session,message.getUserId());
//        MsgType msgType = MsgType.from(message.getType());
//        if (msgType == null) {
//            logger.warn("Unhandled message type, groupId:{}, message:{}", groupId, message);
//            return;
//        }
//        try {
//            ResponseVo<?> responseVo = messageHandlerService.handler(msgType, session, message);
//            if (responseVo != null) {
//                responseVo.setSeq(message.getSeq());
//                responseVo.setType(message.getType());
//                ResponseUtils.sendResponse(session, responseVo);
//                if (lzConfig.isWebSocketDebugLog()) {
//                    long endTime = System.currentTimeMillis();
//                    logger.info("Reply websocket message, groupId:{}, time:{}, message:{}, response:{}", groupId, (endTime - startTime), message, responseVo);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("message processing exception, groupId:{}, message:{}, errorMessage:{}", groupId, message, e.getMessage(), e);
//            ResponseUtils.sendResponse(session, message.getType(), message.getSeq(), MsgCode.ERROR);
//        }
//    }
//
//    /**
//     * 发生异常时
//     *
//     * @param session 会话
//     * @param groupId 连接组ID
//     * @param error   异常对象
//     */
//    @OnError
//    public void onError(Session session, @PathParam("groupId") String groupId, Throwable error) {
//        WebSocketRouteUtils.setRealUserIpToContainer(session);
//        logger.info("on error, groupId:{}, userIP:{}, msg:{}", groupId, SessionUtils.getUserIp(session), error.getMessage(), error);
//    }
//
//    /**
//     * 连接关闭时
//     *
//     * @param session 会话
//     * @param groupId 连接组ID
//     */
//    @OnClose
//    public void onClose(Session session, @PathParam("groupId") String groupId) {
//        WebSocketRouteUtils.setRealUserIpToContainer(session);
//        logger.info("on close, groupId:{}, userIP:{}", groupId, SessionUtils.getUserIp(session));
//    }
//
//    /**
//     * 校验SealToken是否合法
//     *
//     * @param appId 业务方appId
//     * @param token SealToken
//     * @return
//     */
//    private boolean tokenVerify(String appId, String token) {
//        Result<SealTokenServiceProto.ResponseVerifySealToken> result = sealTokenService.verifySealToken(token, appId);
//        if (result.rCode() != SealTokenService.VERIFY_SEAL_TOKEN_SUCCESS) {
//            logger.warn("Failed to verify token, rCode:{}, appId:{}, token:{}", result.rCode(), appId, token);
//            return false;
//        }
//        return true;
//    }
//}