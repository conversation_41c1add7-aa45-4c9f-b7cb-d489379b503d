package fm.lizhi.ocean.seal.websocket.vo;

import lombok.Data;
import lombok.ToString;

/**
 * Created in 2022-05-09 10:06.
 *
 * <AUTHOR>
 */
@Data
@ToString
public class UserVo {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 座位号
     */
    private int seat;
    /**
     * 用户状态
     */
    private int state;
    /**
     * 最后活跃时间
     */
    private String lastActiveTime;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 0：未知 1：男 2：女
     */
    private int gender;
    /**
     * 头像地址
     */
    private String portrait;
    /**
     * 角色
     */
    private int role;
}