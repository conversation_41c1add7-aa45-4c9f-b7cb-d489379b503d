package fm.lizhi.ocean.seal.websocket.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-06-07 19:09.
 *
 * <AUTHOR>
 */
public enum GameState {
    //
    WAIT_PAGE(0, "等待页"),
    GAME_PAGE(1, "游戏页"),
    ;
    private int value;
    private String msg;

    private static Map<Integer, GameState> map = new HashMap<>();

    static {
        for (GameState object : GameState.values()) {
            map.put(object.getValue(), object);
        }
    }

    GameState(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static GameState from(int value) {
        return map.get(value);
    }
}