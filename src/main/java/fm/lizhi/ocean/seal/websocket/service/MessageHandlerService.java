//package fm.lizhi.ocean.seal.websocket.service;
//
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.handler.MessageHandler;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.BeansException;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.ApplicationContextAware;
//import org.springframework.stereotype.Service;
//
//import javax.websocket.Session;
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * Created in 2022-05-07 11:29.
// *
// * <AUTHOR>
// */
//@Service
//public class MessageHandlerService implements ApplicationContextAware {
//    private static final Logger logger = LoggerFactory.getLogger(MessageHandlerService.class);
//    private final Map<MsgType, MessageHandler> handlerMap = new HashMap<>();
//
//    @Override
//    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
//        String[] names = applicationContext.getBeanNamesForType(MessageHandler.class);
//        for (String name : names) {
//            MessageHandler messageHandler = (MessageHandler) applicationContext.getBean(name);
//            this.handlerMap.put(messageHandler.acceptType(), messageHandler);
//        }
//        logger.info("Initialize the websocket message handler, handlers:{}", handlerMap);
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param msgType 消息类型
//     * @param session 会话
//     * @param message 请求体
//     * @return
//     */
//    public ResponseVo<?> handler(MsgType msgType, Session session, Message message) {
//        MessageHandler messageHandler = this.handlerMap.get(msgType);
//        if (messageHandler == null) {
//            logger.error("message handler is null, groupId:{}, msgType:{}, message:{}", SessionUtils.getGroupId(session), msgType, message);
//            return ResponseVo.msgCode(MsgCode.ERROR_MSG_TYPE);
//        }
//        return messageHandler.handler(session, message);
//    }
//}