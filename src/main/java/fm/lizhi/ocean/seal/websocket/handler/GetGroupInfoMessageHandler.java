//package fm.lizhi.ocean.seal.websocket.handler;
//
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.websocket.adapter.UserAdapter;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.dto.User;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.service.WsGameService;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.GameInfo;
//import fm.lizhi.ocean.seal.websocket.vo.GroupInfo;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//import java.util.List;
//
///**
// * 获取当前组信息
// * <p>
// * Created in 2022-05-09 10:36.
// *
// * <AUTHOR>
// */
//@Service
//public class GetGroupInfoMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(GetGroupInfoMessageHandler.class);
//    @Resource
//    private LzConfig lzConfig;
//    @Resource
//    private WsUserService wsUserService;
//    @Resource
//    private UserAdapter userAdapter;
//    @Resource
//    private WsGameService wsGameService;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.GET_GROUP_INFO;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        String groupId = SessionUtils.getGroupId(session);
//        List<User> users = this.wsUserService.getGroupUsers(groupId);
//        GroupInfo groupInfo = new GroupInfo();
//        GameInfo gameInfo = this.wsGameService.getGameInfo(groupId);
//        groupInfo.setUsers(this.userAdapter.converts(users));
//        groupInfo.setGameInfo(gameInfo);
//        if (this.lzConfig.isWebSocketDebugLog()) {
//            logger.info("Get group info, groupId:{}, userId:{}, result:{}", groupId, message.getUserId(), groupInfo);
//        }
//        return ResponseVo.success(groupInfo);
//    }
//}