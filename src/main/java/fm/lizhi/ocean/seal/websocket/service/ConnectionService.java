//package fm.lizhi.ocean.seal.websocket.service;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import fm.lizhi.common.datastore.redis.client.RedisClient;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.constant.RedisNamespace;
//import fm.lizhi.ocean.seal.websocket.constants.RedisKeys;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.dto.User;
//import fm.lizhi.ocean.seal.websocket.dto.UserSession;
//import fm.lizhi.ocean.seal.websocket.param.HeartbeatParam;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.param.SetUserInfoParam;
//import fm.lizhi.ocean.seal.websocket.utils.RedisLock;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.GameInfo;
//import io.netty.util.concurrent.DefaultThreadFactory;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import javax.websocket.Session;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Random;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.Executors;
//import java.util.concurrent.ScheduledExecutorService;
//import java.util.concurrent.TimeUnit;
//
///**
// * Created in 2022-04-29 15:51.
// *
// * <AUTHOR>
// */
//@Service
//public class ConnectionService {
//    private static final Logger logger = LoggerFactory.getLogger(ConnectionService.class);
//    /**
//     * groupId: userId, context
//     */
//    private final ConcurrentHashMap<String, ConcurrentHashMap<Long, UserSession>> groupMap = new ConcurrentHashMap<>();
//    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(2, new DefaultThreadFactory("connection-clean"));
//    @Resource(name = RedisNamespace.REDIS_OCEAN_SEAL)
//    private RedisClient redisClient;
//    private Random random = new Random();
//    @Resource
//    private LzConfig lzConfig;
//    @Resource
//    private BroadCastService broadCastService;
//
//    /**
//     * 初始化时
//     */
//    @PostConstruct
//    private void init() {
//        this.scheduledExecutorService.scheduleAtFixedRate(() -> {
//            try {
//                // 清理过期用户
//                cleanExpireUserConnection();
//            } catch (Exception e) {
//                logger.error("Failed to clean up expired user connections, msg:{}", e.getMessage(), e);
//            }
//        }, 35, this.lzConfig.getWebSocketCleanPeriod(), TimeUnit.SECONDS);
//    }
//
//    /**
//     * 清理过期用户连接
//     */
//    public void cleanExpireUserConnection() {
//        logger.debug("Start cleaning up expired user connections");
//        // 依托于内存中的groupId来寻找内存中的映射，如果在内存中没有的，说明已经是不活跃的redis存储，则依靠redis的过期机制过期掉即可
//        List<String> groupIds = new ArrayList<>();
//        // 清理当前内存连接
//        for (Map.Entry<String, ConcurrentHashMap<Long, UserSession>> entry : groupMap.entrySet()) {
//            String groupId = entry.getKey();
//            groupIds.add(groupId);
//            ConcurrentHashMap<Long, UserSession> sessionMap = entry.getValue();
//            for (Map.Entry<Long, UserSession> sessionEntry : sessionMap.entrySet()) {
//                Long userId = sessionEntry.getKey();
//                UserSession userSession = sessionEntry.getValue();
//                // 如果上次活跃的时间距离当前时间已经超过设置的阈值，则认为是无活跃
//                long diff = (System.currentTimeMillis() - userSession.getLastActiveTime()) / 1000;
//                if (diff > this.lzConfig.getWebSocketConnectExpire()) {
//                    sessionMap.remove(userId);
//                    this.broadCastService.broadcastUserStateChange(groupId);
//                    logger.info("Clean up expired user connections from memory, time:{}, userId:{}", diff, userSession.getUserId());
//                }
//            }
//            if (sessionMap.isEmpty()) {
//                groupMap.remove(groupId);
//                logger.info("Clean up user connection groups from memory, groupId:{}", groupId);
//            }
//        }
//
//        // 清理Redis中存储的连接信息，每个实例都有自己的group
//        for (String groupId : groupIds) {
//            List<String> removeUserIds = new ArrayList<>();
//            String key = RedisKeys.WEBSOCKET_GROUP.buildKey(groupId);
//            Map<String, String> map = this.redisClient.hgetAll(key);
//            for (Map.Entry<String, String> entry : map.entrySet()) {
//                String userId = entry.getKey();
//                User user = JSON.parseObject(entry.getValue(), User.class);
//                // 如果上次活跃的时间距离当前时间已经超过设置的阈值，则认为是无活跃
//                long diff = (System.currentTimeMillis() - user.getLastActiveTime()) / 1000;
//                if (diff > this.lzConfig.getWebSocketConnectExpire()) {
//                    this.broadCastService.broadcastUserStateChange(groupId);
//                    removeUserIds.add(userId);
//                    this.redisClient.hdel(key, userId);
//                    logger.info("Clean up expired user connections from redis, time:{}, user:{}", diff, user);
//                }
//            }
//
//            // 计算是否已经为空集合
//            for (String removeUserId : removeUserIds) {
//                map.remove(removeUserId);
//            }
//            if (map.isEmpty()) {
//                this.redisClient.del(key);
//                logger.info("Clean up user connection groups from redis, groupId:{}, key:{}", groupId, key);
//            }
//        }
//        logger.debug("Clean up expired user connection ends");
//    }
//
//    /**
//     * 获取连接组信息
//     *
//     * @param groupId 组ID
//     * @return
//     */
//    public Map<Long, UserSession> getConnectionGroup(String groupId) {
//        return this.groupMap.get(groupId);
//    }
//
//    /**
//     * 更新用户最新活跃时间
//     *
//     * @param session 会话
//     * @param userId  用户ID
//     */
//    public void updateLastActiveTime(Session session, long userId) {
//        String groupId = SessionUtils.getGroupId(session);
//        ConcurrentHashMap<Long, UserSession> sessionMap = this.groupMap.get(groupId);
//        // 没有group时
//        if (sessionMap == null) {
//            sessionMap = new ConcurrentHashMap<>();
//            ConcurrentHashMap<Long, UserSession> sessionMapTemp = this.groupMap.putIfAbsent(groupId, sessionMap);
//            // 在此之前已经存在对象
//            if (sessionMapTemp != null) {
//                sessionMap = sessionMapTemp;
//            }
//        }
//
//        // 获取当前组所在的用户
//        UserSession userSession = sessionMap.get(userId);
//        if (userSession == null) {
//            userSession = new UserSession();
//            UserSession userSessionTemp = sessionMap.putIfAbsent(userId, userSession);
//            // 在此之前已经存在对象
//            if (userSessionTemp != null) {
//                userSession = userSessionTemp;
//            }
//        }
//
//        // 更新用户最新活跃时间
//        userSession.setSession(session);
//        userSession.setUserId(userId);
//        userSession.setLastActiveTime(System.currentTimeMillis());
//    }
//}