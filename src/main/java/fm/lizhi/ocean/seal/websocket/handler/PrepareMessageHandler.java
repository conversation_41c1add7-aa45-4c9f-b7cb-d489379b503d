//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.param.PrepareParam;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//
///**
// * 准备游戏
// * <p>
// * Created in 2022-05-09 10:36.
// *
// * <AUTHOR>
// */
//@Service
//public class PrepareMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(PrepareMessageHandler.class);
//    @Resource
//    private LzConfig lzConfig;
//    @Resource
//    private WsUserService wsUserService;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.PREPARE;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        String groupId = SessionUtils.getGroupId(session);
//        PrepareParam param = JSON.parseObject(message.getData(), PrepareParam.class);
//        long userId = message.getUserId();
//        if (this.lzConfig.isWebSocketDebugLog()) {
//            logger.info("Prepare, groupId:{}, userId:{}, param:{}", groupId, userId, param);
//        }
//        boolean success = this.wsUserService.updateUserState(groupId, userId, UserState.READY);
//        if (!success) {
//            return ResponseVo.msgCode(MsgCode.ERROR_STATE_CHANGE);
//        }
//        return ResponseVo.success();
//    }
//}