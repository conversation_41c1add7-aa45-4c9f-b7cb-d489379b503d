package fm.lizhi.ocean.seal.websocket.utils;

import fm.lizhi.biz.data.collector.BizDataCtx;
import fm.lizhi.biz.data.collector.DataCtxContainer;
import fm.lizhi.biz.data.collector.HttpCtx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.websocket.EndpointConfig;
import javax.websocket.Session;

/**
 * WebSocket路由工具类
 * <p>
 * Created in 2022-05-07 10:49.
 *
 * <AUTHOR>
 */
public class WebSocketRouteUtils {
    private static final Logger logger = LoggerFactory.getLogger(WebSocketRouteUtils.class);
    public static final String REAL_USER_IP_ATTR = "real-user-ip";

    /**
     * 将用户IP设置到session中，方便后续取用
     *
     * @param session 会话
     * @param config  配置
     */
    public static void setRealUserIpToSession(Session session, EndpointConfig config) {
        String userIp = (String) config.getUserProperties().get(REAL_USER_IP_ATTR);
        if (!"".equals(userIp) && userIp != null) {
            session.getUserProperties().put(REAL_USER_IP_ATTR, userIp);
        }
    }

    /**
     * 设置用户真实IP到路由容器中
     *
     * @param session 会话
     */
    public static void setRealUserIpToContainer(Session session) {
        if (session.getUserProperties().containsKey(REAL_USER_IP_ATTR)) {
            String userIp = (String) session.getUserProperties().get(REAL_USER_IP_ATTR);
            HttpCtx httpCtx = new HttpCtx();
            httpCtx.setIp(userIp);
            BizDataCtx bizDataCtx = new BizDataCtx();
            bizDataCtx.setTimestamp(System.currentTimeMillis());
            bizDataCtx.setHttpCtx(httpCtx);
            DataCtxContainer.init(bizDataCtx);
        }
    }
}