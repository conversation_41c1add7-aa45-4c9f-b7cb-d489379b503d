package fm.lizhi.ocean.seal.websocket.vo;

import fm.lizhi.ocean.seal.websocket.constants.GameState;
import fm.lizhi.ocean.seal.websocket.constants.UserState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-26 19:23.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class GameInfo {
    /**
     * 游戏状态
     */
    private int state = GameState.WAIT_PAGE.getValue();
}