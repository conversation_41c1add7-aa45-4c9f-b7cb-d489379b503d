package fm.lizhi.ocean.seal.websocket.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-06 14:27.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class User {
    /**
     * 用户ID
     */
    private long userId;
    /**
     * 座位号
     */
    private int seat;
    /**
     * 角色
     */
    private int role;
    /**
     * 用户状态
     *
     * @see fm.lizhi.ocean.seal.websocket.constants.UserState
     */
    private int state;
    /**
     * 最后活跃时间，毫秒数
     */
    private long lastActiveTime;
    /**
     * 用户昵称
     */
    private String nickName;
    /**
     * 0：未知 1：男 2：女
     */
    private int gender;
    /**
     * 头像地址
     */
    private String portrait;
}