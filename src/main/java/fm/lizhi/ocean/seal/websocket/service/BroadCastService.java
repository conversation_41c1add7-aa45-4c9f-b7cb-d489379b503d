//package fm.lizhi.ocean.seal.websocket.service;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import fm.lizhi.common.datastore.redis.client.RedisClient;
//import fm.lizhi.ocean.seal.constant.RedisNamespace;
//import fm.lizhi.ocean.seal.websocket.constants.EventType;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.constants.RedisKeys;
//import fm.lizhi.ocean.seal.websocket.dto.Event;
//import fm.lizhi.ocean.seal.websocket.dto.Group;
//import fm.lizhi.ocean.seal.websocket.dto.UserSession;
//import fm.lizhi.ocean.seal.websocket.utils.ResponseUtils;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//import redis.clients.jedis.JedisPubSub;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.Map;
//
///**
// * 广播服务
// * <p>
// * Created in 2022-05-06 15:57.
// *
// * <AUTHOR>
// */
//@Service
//public class BroadCastService {
//    private static final Logger logger = LoggerFactory.getLogger(BroadCastService.class);
//    @Resource(name = RedisNamespace.REDIS_OCEAN_SEAL)
//    private RedisClient redisClient;
//    @Resource
//    private ConnectionService connectionService;
//
//    /**
//     * 初始化时，订阅事件通道
//     */
//    @PostConstruct
//    public void init() {
//        new Thread(() -> {
//            String key = RedisKeys.WEBSOCKET_EVENT_CHANNEL.buildKey("");
//            this.redisClient.subscribe(new JedisPubSub() {
//                @Override
//                public void onMessage(String channel, String message) {
//                    try {
//                        logger.info("Received event message, channel:{}, message:{}", channel, message);
//                        JSONObject jsonObject = JSON.parseObject(message);
//                        int type = jsonObject.getIntValue("eventType");
//                        EventType eventType = EventType.from(type);
//                        if (eventType == null) {
//                            logger.error("Failed to process event message, type is null, message:{}", message);
//                            return;
//                        }
//                        handlerEvent(eventType, jsonObject);
//                    } catch (Exception e) {
//                        logger.error("Failed to process event message, channel:{}, message:{}, errorMessage:{}", channel, message, e.getMessage(), e);
//                    }
//                }
//            }, key);
//        }, "redis-subscribe").start();
//    }
//
//    /**
//     * 处理事件消息
//     *
//     * @param type 事件类型
//     * @param data 事件数据
//     */
//    private void handlerEvent(EventType type, JSONObject data) {
//        Group group = data.getObject("data", Group.class);
//        // 找出当前组用户所在的连接，然后推送一条用户状态变更消息
//        Map<Long, UserSession> sessionMap = this.connectionService.getConnectionGroup(group.getGroupId());
//        logger.info("Handler event, groupId:{}, type:{}, data:{}, sessionMap:{}", group.getGroupId(), type, data, sessionMap);
//        if (sessionMap == null) {
//            logger.warn("Failed to process event, connection group information is empty, groupId:{}", group.getGroupId());
//            return;
//        }
//        for (UserSession userSession : sessionMap.values()) {
//            Event<Object> event = Event.builder().eventType(type.getCode()).build();
//            ResponseVo<Event<Object>> responseVo = ResponseVo.success(event);
//            responseVo.setSeq("push");
//            responseVo.setType(MsgType.EVENT_PUSH.getCode());
//            ResponseUtils.sendResponse(userSession.getSession(), responseVo);
//            logger.info("Push events to user, groupId:{}, userId:{}, response:{}", group.getGroupId(), userSession.getUserId(), responseVo);
//        }
//    }
//
//    /**
//     * 广播用户状态变更
//     *
//     * @param groupId 组Id
//     */
//    public void broadcastUserStateChange(String groupId) {
//        this.broadcastEvent(groupId, EventType.USER_STATE_CHANGE);
//    }
//
//    /**
//     * 开始游戏
//     *
//     * @param groupId 组ID
//     */
//    public void broadcastStartGame(String groupId) {
//        this.broadcastEvent(groupId, EventType.START_GAME);
//    }
//
//    /**
//     * 广播事件
//     *
//     * @param groupId   组ID
//     * @param eventType 事件类型
//     */
//    private void broadcastEvent(String groupId, EventType eventType) {
//        Group group = new Group(groupId);
//        Event<Object> event = Event.builder().eventType(eventType.getCode()).data(group).build();
//        String jsonData = JSON.toJSONString(event);
//        this.redisClient.publish(RedisKeys.WEBSOCKET_EVENT_CHANNEL.buildKey(""), jsonData);
//    }
//}