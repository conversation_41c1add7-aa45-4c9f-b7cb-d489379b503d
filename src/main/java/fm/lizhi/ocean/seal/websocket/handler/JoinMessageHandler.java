//package fm.lizhi.ocean.seal.websocket.handler;
//
//import com.alibaba.fastjson.JSON;
//import fm.lizhi.common.datastore.redis.client.RedisClient;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.constant.RedisNamespace;
//import fm.lizhi.ocean.seal.service.GameFacadeService;
//import fm.lizhi.ocean.seal.websocket.constants.MsgType;
//import fm.lizhi.ocean.seal.websocket.constants.UserState;
//import fm.lizhi.ocean.seal.websocket.dto.BizResult;
//import fm.lizhi.ocean.seal.websocket.dto.GameConfig;
//import fm.lizhi.ocean.seal.websocket.dto.User;
//import fm.lizhi.ocean.seal.websocket.param.JoinParam;
//import fm.lizhi.ocean.seal.websocket.param.Message;
//import fm.lizhi.ocean.seal.websocket.service.BroadCastService;
//import fm.lizhi.ocean.seal.websocket.service.ConnectionService;
//import fm.lizhi.ocean.seal.websocket.service.WsUserService;
//import fm.lizhi.ocean.seal.websocket.utils.RedisLock;
//import fm.lizhi.ocean.seal.websocket.utils.SessionUtils;
//import fm.lizhi.ocean.seal.websocket.vo.MsgCode;
//import fm.lizhi.ocean.seal.websocket.vo.ResponseVo;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import javax.websocket.Session;
//import java.util.List;
//
///**
// * 加入游戏
// * <p>
// * Created in 2022-05-09 10:36.
// *
// * <AUTHOR>
// */
//@Service
//public class JoinMessageHandler implements MessageHandler {
//    private static final Logger logger = LoggerFactory.getLogger(JoinMessageHandler.class);
//    @Resource
//    private WsUserService wsUserService;
//    @Resource
//    private LzConfig lzConfig;
//    @Resource
//    private GameFacadeService gameFacadeService;
//    @Resource
//    private RedisLock redisLock;
//    @Resource
//    private BroadCastService broadCastService;
//
//    /**
//     * 处理的消息类型
//     *
//     * @return
//     */
//    @Override
//    public MsgType acceptType() {
//        return MsgType.JOIN;
//    }
//
//    /**
//     * 消息处理
//     *
//     * @param session 会话
//     * @param message 消息体
//     * @return
//     */
//    @Override
//    public ResponseVo<?> handler(Session session, Message message) {
//        String groupId = SessionUtils.getGroupId(session);
//        JoinParam param = JSON.parseObject(message.getData(), JoinParam.class);
//        String gameId = param.getGameId();
//        long userId = message.getUserId();
//        if (this.lzConfig.isWebSocketDebugLog()) {
//            logger.info("Join group, groupId:{}, userId:{}, param:{}", groupId, userId, param);
//        }
//        if (StringUtils.isEmpty(gameId)) {
//            return ResponseVo.msgCode(MsgCode.ERROR_PARAMS);
//        }
//        BizResult<GameConfig> gameConfig = this.gameFacadeService.getGameConfig(message.getAppId(), gameId);
//        if (gameConfig.isNotSuccess()) {
//            return ResponseVo.msgCode(gameConfig.getCode(), gameConfig.getMsg());
//        }
//        int count = this.wsUserService.getPlayerCount(groupId);
//        if (count >= gameConfig.getData().getMaxPlayer()) {
//            logger.info("Failed to join the game, player is full, groupId:{}, gameId:{}, count:{}, maxCount:{}", groupId, gameId, count, gameConfig.getData().getMaxPlayer());
//            return ResponseVo.msgCode(MsgCode.ERROR_PLAYER_FULL);
//        }
//        boolean success = this.joinGame(groupId, userId, param.getSeat(), UserState.JOINED_NOT_READY);
//        if (!success) {
//            logger.info("Failed to join the game, update seat and state error, groupId:{}, userId:{}, params:{}", groupId, userId, param);
//            return ResponseVo.msgCode(MsgCode.ERROR_JOIN_GAME);
//        }
//        return ResponseVo.success();
//    }
//
//    /**
//     * 加入游戏
//     *
//     * @param groupId 组ID
//     * @param userId  用户ID
//     * @param seat    座位号
//     * @param state   状态
//     * @return
//     */
//    private boolean joinGame(String groupId, long userId, int seat, UserState state) {
//        User user = this.wsUserService.getUser(groupId, userId);
//        if (user != null) {
//            if (user.getSeat() != seat) {
//                boolean success = this.redisLock.lock(groupId, String.valueOf(userId), 10 * 1000, 3 * 1000);
//                if (!success) {
//                    return false;
//                }
//                try {
//                    // 检测其他位置是否有人
//                    List<User> users = this.wsUserService.getGroupUsers(groupId);
//                    for (User temp : users) {
//                        if (temp.getSeat() == seat && seat != 0) {
//                            return false;
//                        }
//                    }
//                    // 更新Redis
//                    user.setSeat(seat);
//                    if (user.getRole() == -1) {
//                        // 观众
//                        user.setRole(0);
//                    }
//                    user.setState(state.getCode());
//                    this.wsUserService.updateUser(groupId, user);
//                    this.broadCastService.broadcastUserStateChange(groupId);
//                    return true;
//                } finally {
//                    this.redisLock.unlock(groupId, String.valueOf(userId));
//                }
//            }else{
//                // 如果座位相同，则直接返回成功
//                return true;
//            }
//        }
//        return false;
//    }
//}