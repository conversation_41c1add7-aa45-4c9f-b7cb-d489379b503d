package fm.lizhi.ocean.seal.websocket.vo;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created in 2022-05-05 15:28.
 *
 * <AUTHOR>
 */
public class ResponseVo<T> {
    /**
     * 消息类型，与请求时保持一致
     *
     * @see fm.lizhi.ocean.seal.websocket.constants.MsgType
     */
    private int type;
    /**
     * 服务端处理状态码
     */
    private int code;
    /**
     * 标识一个唯一的请求，前端生成，服务端回传
     */
    private String seq;
    /**
     * 提示信息
     */
    private String msg;
    /**
     * 数据体
     */
    private T data;

    public ResponseVo() {
    }

    public ResponseVo(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResponseVo(MsgCode msgCode) {
        this.code = msgCode.getCode();
        this.msg = msgCode.getMsg();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "ResponseVo{" +
                "type=" + type +
                ", code=" + code +
                ", seq='" + seq + '\'' +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 成功
     *
     * @param <T>  类型参数
     * @param data 响应数据
     * @return
     */
    public static <T> ResponseVo<T> success(T data) {
        ResponseVo<T> response = new ResponseVo<>(MsgCode.SUCCESS);
        response.setData(data);
        return response;
    }

    /**
     * 成功
     *
     * @param <T>  类型参数
     * @return
     */
    public static <T> ResponseVo<T> success() {
        return new ResponseVo<>(MsgCode.SUCCESS);
    }

    public static <T> ResponseVo<T> msgCode(MsgCode msgCode) {
        return new ResponseVo<>(msgCode);
    }

    public static ResponseVo<Void> msgCode(int code, String msg) {
        return new ResponseVo<>(code, msg);
    }

    @JSONField(serialize = false)
    public boolean isSuccess() {
        return MsgCode.SUCCESS.getCode() == this.code;
    }
}