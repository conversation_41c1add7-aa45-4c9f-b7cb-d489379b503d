package fm.lizhi.ocean.seal.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpHeaders;

/**
 * <AUTHOR>
 */
public class SealSignUtils {

    public static final String SEAL_SIGN_APP_ID = "Seal-Auth-AppId";
    public static final String SEAL_SIGN_TIMESTAMP = "Seal-Auth-Timestamp";
    public static final String SEAL_SIGN_SIGN = "Seal-Auth-Sign";

    /**
     * 添加sign到请求头
     */
    public static HttpHeaders addSignHeaders(HttpHeaders httpHeaders, String sealAppId, String sealAppKey) {

        String timestamp = String.valueOf(System.currentTimeMillis());

        httpHeaders.add(SEAL_SIGN_APP_ID, sealAppId);
        httpHeaders.add(SEAL_SIGN_TIMESTAMP, timestamp);

        String signContent = String.format("%s\n%s\n%s\n", sealAppKey, sealAppId, timestamp);

        String sign = DigestUtils.md5Hex(signContent);

        httpHeaders.add(SEAL_SIGN_SIGN, sign);

        return httpHeaders;
    }

}
