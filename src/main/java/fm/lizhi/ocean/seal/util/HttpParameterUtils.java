package fm.lizhi.ocean.seal.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class HttpParameterUtils {


    /**
     * 从HttpServletRequest中提取所有请求头，并转换为Spring的HttpHeaders对象
     *
     * @param request 当前的HttpServletRequest
     * @return HttpHeaders对象
     */
    public static HttpHeaders extractHttpHeaders(HttpServletRequest request) {
        HttpHeaders headers = new HttpHeaders();

        // 遍历所有请求头
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            Enumeration<String> headerValues = request.getHeaders(headerName);

            // 将所有同名的请求头值添加到HttpHeaders对象中
            while (headerValues.hasMoreElements()) {
                headers.add(headerName, headerValues.nextElement());
            }
        }

        return headers;
    }

    /**
     * 将请求参数拼接到指定的URL上
     *
     * @param request 当前的HttpServletRequest
     * @param baseUrl 基础URL
     * @return 拼接后的完整URL
     */
    public static String appendParametersToUrl(HttpServletRequest request, String baseUrl) {
        // 获取请求的所有参数
        Map<String, String[]> parameterMap = request.getParameterMap();

        if (MapUtils.isEmpty(parameterMap)) {
            return baseUrl;
        }

        StringBuilder urlBuilder = new StringBuilder(baseUrl);

        // 检查URL是否已经包含查询参数
        boolean hasQueryParams = baseUrl.contains("?");

        // 遍历参数并拼接到URL
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String paramName = entry.getKey();
            String[] paramValues = entry.getValue();

            // 遍历每个参数值
            for (String paramValue : paramValues) {
                // 如果URL已经包含查询参数，使用&拼接；否则使用?
                if (hasQueryParams) {
                    urlBuilder.append("&");
                } else {
                    urlBuilder.append("?");
                    hasQueryParams = true;
                }

                // 对参数名和参数值进行URL编码
                try {
                    urlBuilder.append(URLEncoder.encode(paramName, "UTF-8"))
                            .append("=")
                            .append(URLEncoder.encode(paramValue, "UTF-8"));
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

        return urlBuilder.toString();
    }

}
