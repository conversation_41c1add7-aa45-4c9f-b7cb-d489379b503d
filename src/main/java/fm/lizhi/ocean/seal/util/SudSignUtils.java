package fm.lizhi.ocean.seal.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <a href="https://docs-gitbook.sud.tech/zh-CN/app/Server/HttpsCallback/CallbackSignatureVerify.html">接口文档<a/>
 *
 * <AUTHOR>
 */
@Slf4j
public class SudSignUtils {

    public static boolean verifySignature(HttpServletRequest request, String sudSecret) throws IOException {
        String requestBody = new String(IOUtils.toByteArray(request.getInputStream()));

        // SudAppId
        String sudAppId = request.getHeader("Sud-AppId");
        // SudTimestamp
        String sudTimestamp = request.getHeader("Sud-Timestamp");
        // SudNonce
        String sudNonce = request.getHeader("Sud-Nonce");
        // SudSignature
        String sudSignature = request.getHeader("Sud-Signature");
        // 请求body (需保证发送方与接收方的数据一致，建议在拦截器里取对应值）
        if (StringUtils.isEmpty(sudAppId) ||
                StringUtils.isEmpty(sudTimestamp) ||
                StringUtils.isEmpty(sudNonce) ||
                StringUtils.isEmpty(sudSignature) ||
                StringUtils.isEmpty(sudSecret)) {
            log.warn("sud缺少验签参数");
            return false;
        }

        // 构造签名串
        String signContent = String.format("%s\n%s\n%s\n%s\n", sudAppId, sudTimestamp, sudNonce, requestBody);

        // 计算签名值
        HmacUtils hmacUtils = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, sudSecret.getBytes());
        String signature = hmacUtils.hmacHex(signContent);

        // 比较签名值 true: 验签成功， false: 验签失败
        return sudSignature.equals(signature);

    }
}
