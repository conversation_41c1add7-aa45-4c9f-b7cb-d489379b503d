package fm.lizhi.ocean.seal.demo.controller;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameProxyService;
import fm.lizhi.ocean.seal.common.pojo.vo.ResultVO;
import fm.lizhi.ocean.seal.common.pojo.vo.SealTokenResp;
import fm.lizhi.ocean.seal.demo.dto.DemoSealTokenRequest;
import fm.lizhi.ocean.seal.demo.service.DemoSealTokenService;
import fm.lizhi.ocean.seal.lizhi.vo.ProxyResult;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("demo-seal-business")
public class DemoBusinessController {

    @Resource
    private DemoSealTokenService demoSealTokenService;

    @Resource
    private GameProxyService gameProxyService;

    /**
     * 这个接口是模拟给web端获取sealToken，所以不用校验什么。
     * 线上是由业务提供的，上线前要把这个接口去掉或者模块化不加载
     */
    @GetMapping("/{appId}/token")
    public ResultVO<SealTokenResp> getSealToken(@PathVariable("appId") String appId,
                                                @RequestParam(name = "userId") String userId) {

        DemoSealTokenRequest request = new DemoSealTokenRequest();
        request.setUid(userId);
        request.setAppId(appId);

        return demoSealTokenService.getSealToken(request);
    }


    /**
     * 调用目标方法
     * "eventName": "game_start"
     * "channel": "sud/other"
     * "params": " {
     * "env": 0,
     * "appId": "1111",
     * "gameId": "2222",
     * "dataJson": {
     * "room_id": "33333",
     * "report_game_info_extras": ""
     * }
     * }"
     *
     * @return
     */
    @PostMapping("/invokeTarget")
    public ResultVO<ProxyResult> invokeTarget(HttpServletRequest request, @RequestBody JSONObject requestJson) {
        ProxyResult response = new ProxyResult();
        String eventName = requestJson.getString("eventName");
        String channel = requestJson.getString("channel");
        int env = requestJson.getIntValue("env");
        String appId = requestJson.getString("appId");
        String gameId = requestJson.getString("gameId");
        JSONObject dataJson = requestJson.getJSONObject("dataJson");

        try {
            Result<GameProxyServiceProto.ResponseInvokeTarget> result = this.gameProxyService.invokeTarget(GameProxyServiceProto.InvokeTargetParams.newBuilder()
                    .setEventName(eventName)
                    .setChannel(channel)
                    .setEnv(env)
                    .setAppId(appId)
                    .setGameId(gameId)
                    .setDataJson(dataJson.toString())
                    .build());

            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("invokeTarget error.`req={}`rCode={}", requestJson, result.rCode());
                return ResultVO.failure(result.rCode());
            } else {
                GameProxyServiceProto.ResponseInvokeTarget target = result.target();
                response.setCode(target.getBizCode());
                response.setMsg(target.getMsg());
                response.setData(target.getData());
                return ResultVO.success(response);
            }
        } catch (Exception e) {
            log.error("Failed to invoke target interface, service exception, requestJson:{}", e);
        }
        return ResultVO.failure(GeneralRCode.GENERAL_RCODE_SERVER_BUSY);
    }
}
