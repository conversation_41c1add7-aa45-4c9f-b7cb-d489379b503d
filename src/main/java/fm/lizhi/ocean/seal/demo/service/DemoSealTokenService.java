package fm.lizhi.ocean.seal.demo.service;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.SealTokenService;
import fm.lizhi.ocean.seal.common.pojo.vo.ResultVO;
import fm.lizhi.ocean.seal.common.pojo.vo.SealTokenResp;
import fm.lizhi.ocean.seal.demo.dto.DemoSealTokenRequest;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DemoSealTokenService {

    @Resource
    private SealTokenService sealTokenService;

    /**
     * 模拟业务获取sealToken
     */
    public ResultVO<SealTokenResp> getSealToken(DemoSealTokenRequest request) {
        log.info("模拟业务获取sealToken：{}", JSON.toJSONString(request));
        String uid = request.getUid();
        String appId = request.getAppId();

        SealTokenResp resp = new SealTokenResp();
        try {
            Result<SealTokenServiceProto.ResponseGetSealHashingToken> result = sealTokenService.getSealHashingToken(SealTokenServiceProto.SealHashingTokenParam.newBuilder()
                    .setAppId(appId)
                    .setCurrentTimeMillis(System.currentTimeMillis())
                    .setNotVerifySign(true)
                    .setUserId(Long.parseLong(uid))
                    .build());
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("get seal token error.`req={}`appId={}`rCode={}", request, appId, result.rCode());
                return ResultVO.failure(result.rCode());
            }

            resp.setSealToken(result.target().getSealToken());
            resp.setExpireDate(result.target().getExpireDate());
            return ResultVO.success(resp);
        } catch (Exception e) {
            log.error("get seal token error.`req={}`appId={}`", request, appId, e);
        }
        return ResultVO.failure(GeneralRCode.GENERAL_RCODE_SERVER_BUSY);

    }
}
