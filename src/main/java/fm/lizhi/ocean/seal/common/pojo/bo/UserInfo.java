package fm.lizhi.ocean.seal.common.pojo.bo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserInfo {

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户名
     */
    private String name;

    /**
     * 性别
     *
     * @see fm.lizhi.ocean.seal.constant.Gender
     * @see fm.lizhi.ocean.seal.constant.Gender
     */
    private Integer gender;

    /**
     * 头像
     */
    private String portrait;
}
