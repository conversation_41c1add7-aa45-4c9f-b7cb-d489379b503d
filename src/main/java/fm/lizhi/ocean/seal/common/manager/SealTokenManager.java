package fm.lizhi.ocean.seal.common.manager;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.SealTokenService;
import fm.lizhi.ocean.seal.common.pojo.param.GameTokenParam;
import fm.lizhi.ocean.seal.common.pojo.param.SealTokenReq;
import fm.lizhi.ocean.seal.common.pojo.vo.GameChannelToken;
import fm.lizhi.ocean.seal.common.pojo.vo.GameTokenResp;
import fm.lizhi.ocean.seal.common.pojo.vo.SealTokenResp;
import fm.lizhi.ocean.seal.common.pojo.vo.SealTokenVerifyResult;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseGetSealHashingToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.ResponseVerifySealToken;
import fm.lizhi.ocean.seal.protocol.SealTokenServiceProto.SealHashingTokenParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class SealTokenManager {
    @Resource
    private SealTokenService sealTokenService;
    @Resource
    private GameAuthService gameAuthService;

    /**
     * 根据业务颁发的appId 验证sealToken
     *
     * @param sealToken
     * @param appId
     * @return
     */
    public boolean verifySealToken(String sealToken, String appId) {
        if (StringUtils.isBlank(sealToken)) {
            return false;
        }

        try {
            Result<ResponseVerifySealToken> result = sealTokenService.verifySealToken(sealToken, appId);
            if (result.rCode() == SealTokenService.VERIFY_SEAL_TOKEN_SUCCESS) {
                return true;
            }

            log.warn("verify seal token error.`sealToken={}`appId={}`rCode={}", sealToken, appId, result.rCode());
        } catch (Exception e) {
            log.warn("verify seal token error.`sealToken={}`appId={}", sealToken, appId, e);
        }
        return false;
    }

    /**
     * 校验sealToken并且返回用户登信息
     */
    public SealTokenVerifyResult verifySealTokenReturnInfo(String sealToken, String appId) {
        if (StringUtils.isBlank(sealToken)) {
            return SealTokenVerifyResult.fail();
        }

        try {
            Result<SealTokenServiceProto.ResponseGetUserInfoBySealToken> result = sealTokenService.getUserInfoBySealToken(sealToken, appId);

            if (result.rCode() == SealTokenService.VERIFY_SEAL_TOKEN_SUCCESS) {
                SealTokenServiceProto.ResponseGetUserInfoBySealToken target = result.target();
                return SealTokenVerifyResult.success(String.valueOf(target.getSealUser().getUserId()));
            }

            log.warn("verify seal token error.`sealToken={}`appId={}`rCode={}", sealToken, appId, result.rCode());
        } catch (Exception e) {
            log.warn("verify seal token error.`sealToken={}`appId={}", sealToken, appId, e);
        }
        return SealTokenVerifyResult.fail();
    }

    /**
     * 根据传入的参数获取SealToken
     *
     * @param req
     * @param appId
     * @return
     */
    public int getSealToken(SealTokenReq req, SealTokenResp resp, String appId) {
        try {
            Result<ResponseGetSealHashingToken> result = sealTokenService.getSealHashingToken(SealHashingTokenParam.newBuilder()
                    .setAppId(appId).setCurrentTimeMillis(req.getCurrentTimeMillis())
                    .setHashingToken(req.getHashingToken()).setUserId(req.getUserId()).build());
            if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
                log.error("get seal token error.`req={}`appId={}`rCode={}", req, appId, result.rCode());
                return result.rCode();
            }

            resp.setSealToken(result.target().getSealToken());
            resp.setExpireDate(result.target().getExpireDate());
            return result.rCode();
        } catch (Exception e) {
            log.error("get seal token error.`req={}`appId={}`", req, appId, e);
        }
        return GeneralRCode.GENERAL_RCODE_SERVER_BUSY;
    }


    public GameChannelToken getGameToken(GameTokenParam request) {
        // 目前只支持sud 和 luk
        if (!GameChannel.SUD.equals(request.getChannel()) && !GameChannel.LUK.equals(request.getChannel())) {
            throw new RuntimeException("channel not support");
        }
        Result<GameAuthServiceProto.ResponseGetLoginToken> codeResult = gameAuthService.getLoginToken(
                Long.parseLong(request.getUserId()),
                request.getAppId(),
                request.getChannel()
        );
        if (codeResult.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("获取渠道code失败：{}", codeResult.getAttachment());
            throw new RuntimeException("error");
        }
        GameAuthServiceProto.LoginToken token = codeResult.target().getToken();

        GameChannelToken channelToken = new GameChannelToken(token.getToken(), token.getExpireDate());
        return channelToken;
    }
}
