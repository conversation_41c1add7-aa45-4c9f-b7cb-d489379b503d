package fm.lizhi.ocean.seal.common.manager;


import com.alibaba.dubbo.rpc.RpcResult;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameInfoService;
import fm.lizhi.ocean.seal.api.GameService;
import fm.lizhi.ocean.seal.common.convert.SealGameConvert;
import fm.lizhi.ocean.seal.common.pojo.param.GetGameLoadConfigParam;
import fm.lizhi.ocean.seal.common.pojo.param.GetSDKConfigParam;
import fm.lizhi.ocean.seal.common.pojo.vo.*;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.GetGameListParam;
import fm.lizhi.ocean.seal.protocol.GameInfoServiceProto.ResponseGetGameList;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SealGameInfoManager {
    @Resource
    private GameInfoService gameInfoService;
    @Resource
    private GameAppInfoManager gameAppInfoManager;
    @Resource
    private GameService gameService;

    /**
     * 根据颁发给业务的appId 查询游戏列表
     *
     * @param appId
     * @return
     */
    public SealGameInfoResp getSealGameInfoRespByAppId(String appId) {
        SealGameInfoResp resp = new SealGameInfoResp();
        try {
            Result<ResponseGetGameList> result
                    = gameInfoService.getGameList(GetGameListParam.newBuilder().setAppId(appId).build());
            if (result.rCode() != GameInfoService.GET_GAME_LIST_SUCCESS) {
                log.warn("get game list error.`appId={}`rCode={}", appId, result.rCode());
                return resp;
            }

            resp.setAppName(result.target().getAppName());
            resp.setAppTopic(result.target().getAppTopic());

            if (CollectionUtils.isEmpty(result.target().getGameInfosList())) {
                resp.setSealGameInfoDetailResps(Collections.EMPTY_LIST);
                return resp;
            }

            resp.setSealGameInfoDetailResps(result.target().getGameInfosList().stream().map(x -> {
                SealGameInfoDetailResp detailResp = new SealGameInfoDetailResp();
                //小游戏平台的游戏ID gameInfoId，业务不需要关心此ID.因为怕引起歧义。sealGameId还是直接=业务关联的游戏ID,然后去除bizGameId
                detailResp.setSealGameId(x.getBizGameId() + StringUtils.EMPTY);
                detailResp.setChannelGameId(x.getChannelGameIdStr());
                detailResp.setName(x.getName());
                detailResp.setDesc(x.getDesc());
                detailResp.setChannel(x.getChannel());
                detailResp.setChannelId(x.getChannelId() + StringUtils.EMPTY);
                detailResp.setConfigJson(x.getConfigJson());
                return detailResp;
            }).collect(Collectors.toList()));

            return resp;
        } catch (Exception e) {
            log.error("get game list error.`appId={}", appId, e);
        }
        return resp;
    }

    public List<InitSDKConfig> getInitSDKConfigBean(GetSDKConfigParam param) {
        String appId = param.getAppId();

        // 获取初始化配置
        GameAppServiceProto.GameAppInfo gameAppInfo = gameAppInfoManager.getGameAppInfo(appId);
        if (null == gameAppInfo) {
            log.warn("getInitSDKConfig info empty, appId: {}", appId);
            return Collections.EMPTY_LIST;
        }

        List<GameAppServiceProto.ChannelInfo> channelInfos = gameAppInfo.getChannelInfosList();
        if (CollectionUtils.isEmpty(channelInfos)) {
            log.warn("getInitSDKConfig channel empty, appId: {}", appId);
            return Collections.EMPTY_LIST;
        }

        List<InitSDKConfig> sdkConfigs = channelInfos.stream().map(x -> {
            InitSDKConfig initSDKConfig = new InitSDKConfig();
            initSDKConfig.setChannel(x.getChannel());
            initSDKConfig.setChannelAppId(x.getChannelAppId());
            initSDKConfig.setChannelAppKey(x.getChannelAppKey());
            initSDKConfig.setApiUrl(x.getBaseUrl());
            initSDKConfig.setWsUrl(x.getWsUrl());
            return initSDKConfig;
        }).collect(Collectors.toList());

        return sdkConfigs;
    }

    public GetSDKConfigResp getInitSDKConfig(GetSDKConfigParam param) {
        String appId = param.getAppId();

        GetSDKConfigResp resp = new GetSDKConfigResp();

        // 获取初始化配置
        GameAppServiceProto.GameAppInfo gameAppInfo = gameAppInfoManager.getGameAppInfo(appId);
        if (null == gameAppInfo) {
            log.warn("getInitSDKConfig info empty, appId: {}", appId);
            return resp;
        }

        List<GameAppServiceProto.ChannelInfo> channelInfos = gameAppInfo.getChannelInfosList();
        if (CollectionUtils.isEmpty(channelInfos)) {
            log.warn("getInitSDKConfig channel empty, appId: {}", appId);
            return resp;
        }

        resp.setInitSDKConfigs(channelInfos.stream().map(x -> {
            InitSDKConfig initSDKConfig = new InitSDKConfig();
            initSDKConfig.setChannel(x.getChannel());
            initSDKConfig.setChannelAppId(x.getChannelAppId());
            initSDKConfig.setChannelAppKey(x.getChannelAppKey());
            return initSDKConfig;
        }).collect(Collectors.toList()));

        return resp;
    }

    /**
     * 获取游戏加载配置
     * @param param
     * @return
     */
    public Optional<GameLoadingConfigVO> getGameLoadingConfig(GetGameLoadConfigParam param){

        GameServiceProto.GetGameLoadingConfigParam request = GameServiceProto.GetGameLoadingConfigParam.newBuilder()
                .setAppId(param.getAppId())
                .setGameId(param.getGameId())
                .build();

        Result<GameServiceProto.ResponseGetGameLoadingConfig> result = gameService.getGameLoadingConfig(request);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            log.warn("get game loading config error.`appId={}`gameId={}`rCode={}", param.getAppId(), param.getGameId(), result.rCode());
            return Optional.empty();
        }

        return Optional.of(SealGameConvert.I.gameLoadingConfigToGameLoadingConfigVO(result.target()));


    }
}
