package fm.lizhi.ocean.seal.common.pojo.param;

import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Data
public class SetterGameInfoParam {
    @NotBlank(message = "游戏局不能为空")
    private String gameRoundId;
    @NotBlank(message = "渠道不能为空")
    private String gameChannel;
    @NotBlank(message = "sealToken不能为空")
    private String sealToken;
}