package fm.lizhi.ocean.seal.common.pojo.vo;

import lombok.*;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class SealGameInfoDetailResp {
    //小游戏平台的游戏ID gameInfoId，业务不需要关心此ID.因为怕引起歧义。sealGameId还是直接=业务关联的游戏ID,然后去除bizGameId
    private String sealGameId;
    //游戏厂商ID，业务不需要关心此ID
    private String channelGameId;
    private String name;
    private String desc;
    private String channel;
    private String channelId;
    private String configJson;
    //业务关联的游戏ID
    //private String bizGameId;
}
