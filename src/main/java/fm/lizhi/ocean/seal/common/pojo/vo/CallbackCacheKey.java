package fm.lizhi.ocean.seal.common.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Data
public class CallbackCacheKey {

    private String appId;

    /**
     * {@link fm.lizhi.ocean.seal.constant.GameCallbackType}
     */
    private Integer callbackType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CallbackCacheKey cacheKey = (CallbackCacheKey) o;
        return Objects.equals(appId, cacheKey.getAppId()) && Objects.equals(callbackType, cacheKey.getCallbackType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, callbackType);
    }
}
