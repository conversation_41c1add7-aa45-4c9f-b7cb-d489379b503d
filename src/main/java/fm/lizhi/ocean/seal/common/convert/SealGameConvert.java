package fm.lizhi.ocean.seal.common.convert;


import fm.lizhi.ocean.seal.common.pojo.vo.ChannelGameInfo;
import fm.lizhi.ocean.seal.common.pojo.vo.GameLoadingConfigVO;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.ERROR,
        imports = {
        }
)
public interface SealGameConvert {

    SealGameConvert I = Mappers.getMapper(SealGameConvert.class);

    @Mapping(target = "url", source = "gameLoadingConfig.gameLoadingConfig.url")
    @Mapping(target = "gameId", source = "gameLoadingConfig.gameLoadingConfig.gameId")
    @Mapping(target = "GZipUrl", source = "gameLoadingConfig.gameLoadingConfig.GZipUrl")
    GameLoadingConfigVO gameLoadingConfigToGameLoadingConfigVO(GameServiceProto.ResponseGetGameLoadingConfig gameLoadingConfig);


    @Mapping(target = "id", source = "gameId")
    ChannelGameInfo gameLoadingConfigVo2ChannelGameInfo(GameLoadingConfigVO gameLoadingConfigVO);

}
