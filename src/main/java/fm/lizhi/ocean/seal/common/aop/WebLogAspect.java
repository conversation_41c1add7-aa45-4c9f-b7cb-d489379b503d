package fm.lizhi.ocean.seal.common.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.codehaus.plexus.util.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 描述：
 * 日志切面
 *
 * <AUTHOR>
 * @date 2019-03-12 10:13
 */
@Slf4j
@Aspect
@Component
public class WebLogAspect {

    /**
     * 拦截所有 controller
     * 两个..代表所有子目录，最后括号里的两个..代表所有参数
     */
    @Pointcut("execution( * fm.lizhi.ocean.seal..controller..*.*(..))")
    public void logPointCut() {
    }

    @Around("logPointCut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        StringBuilder url = new StringBuilder(request.getRequestURL());
        String queryString = request.getQueryString();
        if (StringUtils.isNotBlank(queryString)) {
            url.append("?").append(queryString);
        }
        // 加上 header 上的 token
        String token = request.getHeader("token");
        if (StringUtils.isNotBlank(token)) {
            url.append(StringUtils.isBlank(queryString) ? "?" : "&").append("token=").append(token);
        }
        long startTime = System.currentTimeMillis();
        log.info("请求开始. url:{}", url);
        // result 为方法的返回值
        Object result = joinPoint.proceed();
        log.info("请求结束. url:{}`返回值:{}`耗时:{}ms", url, result, System.currentTimeMillis() - startTime);
        return result;
    }

}

