package fm.lizhi.ocean.seal.common.manager;

import com.google.common.base.Optional;
import com.google.common.base.Strings;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameAppService;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.GameAppInfo;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto.ResponseGetAppInfoByAppId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class GameAppInfoManager {
    @Resource
    private GameAppService gameAppService;

    private LoadingCache<String, Optional<GameAppInfo>> gameAppInfoCache;

    @PostConstruct
    private void init() {
        /**
         * 缓存
         */
        gameAppInfoCache = CacheBuilder.newBuilder().maximumSize(50)
                .expireAfterWrite(24, TimeUnit.HOURS)
                .build(new CacheLoader<String, Optional<GameAppInfo>>() {
                    @Override
                    public Optional<GameAppInfo> load(String key) {
                        GameAppInfo gameAppInfo = loadGameAppInfo(key);
                        if (gameAppInfo == null) {
                            return Optional.absent();
                        }
                        return Optional.of(gameAppInfo);
                    }
                });
    }


    /**
     * 根据appId获取Seal平台颁发给业务的App信息
     * @param appId
     * @return
     */
    public GameAppInfo getGameAppInfo(String appId) {
        if (Strings.isNullOrEmpty(appId)) {
            return null;
        }
        Optional<GameAppInfo> optional = gameAppInfoCache.getUnchecked(appId);
        if (optional.isPresent()) {
            return optional.get();
        }
        return null;
    }


    private GameAppInfo loadGameAppInfo(String appId) {
        try {
            Result<ResponseGetAppInfoByAppId> result = gameAppService.getAppInfoByAppId(appId);
            if(result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS){
                return result.target().getGameAppInfo();
            }
            log.warn("load game app info error.`appId={}`rCode={}", appId, result.rCode());
        }catch (Exception e){
            log.error("load game app info error.`appId={}", appId, e);
        }
        return null;
    }
}
