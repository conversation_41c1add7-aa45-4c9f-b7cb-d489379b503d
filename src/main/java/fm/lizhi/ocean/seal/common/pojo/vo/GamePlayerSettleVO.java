package fm.lizhi.ocean.seal.common.pojo.vo;

import lombok.*;

/**
 * optional string uid = 1; //游戏用户ID
 * optional bool realUser = 2; //是否是真实用户
 *     optional bool escaped = 3 ; //是否逃跑
 *     optional int32 rank = 4; //排名，从1开始，平局排名相同
 *     optional int32 score = 5; //得分
 *     optional int32 role = 6; //游戏角色
 *     optional int32 isWin = 7; //胜负结果
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class GamePlayerSettleVO {
    private String uid;
    private Boolean realUser;
    private Boolean escaped;
    private Integer rank;
    private Integer score;
    private Integer role;
    private Integer isWin;
}
