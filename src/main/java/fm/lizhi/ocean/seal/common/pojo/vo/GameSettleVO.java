package fm.lizhi.ocean.seal.common.pojo.vo;

import lombok.*;

import java.util.List;

/**
 *optional int64 gameId = 1; //游戏厂商ID
 *     optional string roomId = 2; //房间ID
 *     optional int32 gameMode = 3; //游戏模式
 *     optional string gameRoundId = 4; //本局游戏的id （可能会重复上报，使用该字段去重）
 *     optional int64 gameStartAtTime = 5; //游戏真正开始时间(毫秒)
 *     optional int64 gameEndAtTime = 6; //游戏真正结束时间(毫秒)
 *     optional int32 gameDuration = 7; //游戏真正的进行时间(秒)
 *     repeated GamePlayerSettleResult gamePlayers = 8; //GamePlayer对象数组
 *     optional string appId = 9; //appID
 *     optional int32 env = 10; //所属环境
 *     optional string reportGameInfoExtras = 11; //游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
 *     optional string channel = 12; //游戏渠道
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class GameSettleVO {
    private String gameId;
    private String roomId;
    private Integer gameMode;
    private String gameRoundId;
    private Long gameStartAtTime;
    private Long gameEndAtTime;
    private Integer gameDuration;
    private String appId;
    private Long env;
    private String reportGameInfoExtras;
    private String channel;
    private List<GamePlayerSettleVO> gamePlayers;
}
