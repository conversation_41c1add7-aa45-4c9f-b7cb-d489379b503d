//package fm.lizhi.ocean.seal.common.mongo;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.stereotype.Service;
//
//@Slf4j
//@Service
//public class ReportGetUserDataMongoDao {
//    @Autowired
//    private MongoTemplate mongoTemplate;
//
//    /**
//     * 插入完成任务流水到mongo
//     *
//     * @param doc
//     */
//    public void insertReportGetUserDataMongo(ReportGetUserDataDOC doc) {
//        try {
//            // mongoTemplate.insert(doc);
//            log.info("[MOCK] insert report_get_user_data to mongo success.`doc={}", doc.toString());
//        } catch (Exception e) {
//            log.error("insert report_get_user_data to mongo fail.`doc={}", doc.toString());
//        }
//    }
//}
