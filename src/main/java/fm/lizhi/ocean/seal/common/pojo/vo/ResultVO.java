package fm.lizhi.ocean.seal.common.pojo.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ResultVO<T> {

    public static final int SUCCESS = 0;

    public static final int DEFAULT_ERROR = -10001;

    public static final int TENANT_ERROR = -10002;

    public static final int TOKEN_ERROR = -10003;

    @Getter(onMethod_ = {@JsonProperty("rCode")})
    private int rCode;

    private String msg;

    private T data;

    public static <T> ResultVO<T> success() {
        return new ResultVO<T>().setRCode(SUCCESS);
    }

    public static <T> ResultVO<T> success(T data) {
        return new ResultVO<T>().setRCode(SUCCESS).setData(data);
    }

    public static <T> ResultVO<T> failure() {
        return new ResultVO<T>().setRCode(DEFAULT_ERROR);
    }

    public static <T> ResultVO<T> failure(int rCode) {
        return new ResultVO<T>().setRCode(rCode);
    }

    public static <T> ResultVO<T> failure(String msg) {
        return new ResultVO<T>().setRCode(DEFAULT_ERROR).setMsg(msg);
    }

    public static <T> ResultVO<T> failure(int rCode, String msg) {
        return new ResultVO<T>().setRCode(rCode).setMsg(msg);
    }
}
