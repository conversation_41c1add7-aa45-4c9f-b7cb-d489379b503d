package fm.lizhi.ocean.seal.common.controller;

import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.common.manager.SealTokenManager;
import fm.lizhi.ocean.seal.common.pojo.vo.ResultVO;
import fm.lizhi.ocean.seal.common.pojo.param.SealTokenReq;
import fm.lizhi.ocean.seal.common.pojo.vo.SealTokenResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 提供https post 获取sealToken的方式
 *
 */
@RestController
@Slf4j
@RequestMapping("seal")
public class SealTokenController {

    @Resource
    private SealTokenManager sealTokenManager;

    /**
     * 根据传入的参数获取SealToken
     * @param appId
     * @param req
     * @return
     */
    @PostMapping("/{appId}/token")
    public ResultVO<SealTokenResp> getSealToken(@PathVariable("appId") String appId, @RequestBody @Validated SealTokenReq req){
        SealTokenResp resp = new SealTokenResp();
        int rCode = sealTokenManager.getSealToken(req, resp, appId);
        return rCode == GeneralRCode.GENERAL_RCODE_SUCCESS ? ResultVO.success(resp) : ResultVO.failure(rCode);
    }


}
