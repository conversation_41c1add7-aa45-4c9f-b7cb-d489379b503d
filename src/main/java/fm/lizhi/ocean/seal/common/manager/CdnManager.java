package fm.lizhi.ocean.seal.common.manager;

import fm.lizhi.ocean.seal.constant.Tenant;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Component
@Deprecated
public class CdnManager {

    @Setter(onMethod_ = {@Autowired})
    private Environment env;

    public String addPortraitCdnHost(Tenant tenant, String resource) {
        String cdnHost = getCdnHost(tenant);
        String portraitPrefix = getPortraitPrefix(tenant);

        log.info("debug.`resource={}`cdnHost={}`portraitPrefix={}", resource, cdnHost, portraitPrefix);
        return UriComponentsBuilder.fromHttpUrl(cdnHost).path(portraitPrefix).path(resource).toUriString();
    }

    private String getCdnHost(Tenant tenant) {
        String configKey = String.format("cdnHost.%s", tenant.getCode());
        return getRequiredConfig(configKey);
    }

    private String getPortraitPrefix(Tenant tenant) {
        String configKey = String.format("portraitPrefix.%s", tenant.getCode());
        return env.getProperty(configKey, String.class, "");
    }

    private String getRequiredConfig(String configKey) {
        String configValue = env.getProperty(configKey);
        if (configValue == null) {
            throw new IllegalStateException(String.format("配置%s为null", configKey));
        }
        return configValue;
    }
}
