//package fm.lizhi.ocean.seal.common.mongo;
//
//import lombok.Data;
//import lombok.ToString;
//import org.springframework.data.annotation.Id;
//import org.springframework.data.mongodb.core.mapping.Document;
//
//@Data
//@ToString
//@Document(collection = "report_get_user_record_doc")
//public class ReportGetUserDataDOC {
//
//    @Id
//    private String id;
//    /**
//     * 用户
//     */
//    private Long userId;
//
//    /**
//     * 事件名称
//     */
//    private String eventName;
//    /**
//     * 渠道
//     */
//    private String channel;
//    /**
//     * appId
//     */
//    private String appId;
//    /**
//     * 创建时间
//     *
//     */
//    private Long createTime;
//
//}
//
