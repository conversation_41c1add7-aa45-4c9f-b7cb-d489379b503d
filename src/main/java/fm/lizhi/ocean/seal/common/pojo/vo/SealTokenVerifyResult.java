package fm.lizhi.ocean.seal.common.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Data
public class SealTokenVerifyResult {

    /**
     * 验证是否成功 true-成功
     */
    private boolean success;

    /**
     * 用户id
     */
    private String userId;

    public static SealTokenVerifyResult fail() {
        return new SealTokenVerifyResult(false, null);
    }

    public static SealTokenVerifyResult success(String userId) {
        return new SealTokenVerifyResult(true, userId);
    }
}
