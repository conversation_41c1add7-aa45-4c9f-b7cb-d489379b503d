package fm.lizhi.ocean.seal.common.manager;


import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.common.pojo.param.SetterGameInfoParam;
import fm.lizhi.ocean.seal.common.pojo.vo.GamePlayerSettleVO;
import fm.lizhi.ocean.seal.common.pojo.vo.GameSettleVO;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.GameSettleResult;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto.ResponseGetGameSettleReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.stream.Collectors;

@Slf4j
@Service
public class QuerySetterGameInfoManager {
    @Resource
    private GameReportService gameReportService;
    private final String ignoreProperties = "";

    /**
     * 根据条件获取第三方游戏结算数据
     * @param appId
     * @param setterGameInfoParam
     * @return
     */
    public GameSettleVO getSetterGameInfo(String appId, SetterGameInfoParam setterGameInfoParam){
        try {
            Result<ResponseGetGameSettleReport> result
                    = gameReportService.getGameSettleReport(setterGameInfoParam.getGameRoundId(), setterGameInfoParam.getGameChannel(), appId);
            if(result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS){
                log.error("get game settle report error.`appId={}`roundId={}`channel={}`rCode={}"
                        , appId, setterGameInfoParam.getGameRoundId(), setterGameInfoParam.getGameChannel(), result.rCode());
                return null;
            }

            return convertPbToVO(result.target().getResult());
        }catch (Exception e){
            log.error("get game settle report error.`appId={}`roundId={}`channel={}"
                    , appId, setterGameInfoParam.getGameRoundId(), setterGameInfoParam.getGameChannel(), e);
        }
        return null;
    }

    private GameSettleVO convertPbToVO(GameSettleResult gameSettleResult){
        GameSettleVO vo = new GameSettleVO();
        BeanUtils.copyProperties(gameSettleResult, vo, "");
        if(CollectionUtils.isEmpty(gameSettleResult.getGamePlayersList())){
            vo.setGamePlayers(Collections.EMPTY_LIST);
            return vo;
        }

        vo.setGamePlayers(gameSettleResult.getGamePlayersList().stream().map(x->{
            GamePlayerSettleVO gamePlayerSettleVO = new GamePlayerSettleVO();
            BeanUtils.copyProperties(x, gamePlayerSettleVO);
            return gamePlayerSettleVO;
        }).collect(Collectors.toList()));
        return vo;
    }
}
