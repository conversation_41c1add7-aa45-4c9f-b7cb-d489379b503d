package fm.lizhi.ocean.seal.common.pojo.param;

import lombok.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Data
public class SealTokenReq {
    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID参数错误")
    private Long userId;
    @NotNull(message = "时间戳不能为空")
    @Min(value = 1, message = "时间戳参数错误")
    private Long currentTimeMillis;
    @NotBlank(message = "加密串不能为空")
    private String hashingToken;
}