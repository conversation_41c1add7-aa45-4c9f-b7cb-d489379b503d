package fm.lizhi.ocean.seal.common.pojo.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.Enumeration;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class HttpForwardRequestContext {

    private HttpMethod method;

    private HttpHeaders headers;

    private byte[] body;

    private MultiValueMap<String, Object> multiValueMap;

    /**
     * 请求路径
     */
    private String requestPath;

    public HttpEntity<?> toHttpEntity() {
        if (body != null && body.length > 0) {
            return new HttpEntity<>(body, headers);
        }
        if (!CollectionUtils.isEmpty(multiValueMap)) {
            return new HttpEntity<>(multiValueMap, headers);
        }
        return new HttpEntity<>(null, headers);
    }

    public void parseRequest(HttpServletRequest request) {
        parseRequest(request, true);
    }

    /**
     * 解析请求参数做透传
     */
    public void parseRequest(HttpServletRequest request, boolean parseBody) {
        if (request == null) {
            throw new NullPointerException("request param is null");
        }
        if (method == null) {
            this.method = HttpMethod.resolve(request.getMethod().toUpperCase());
        }

        if (parseBody) {
            byte[] body = readBodyToByteArray(request);
            if (body != null && body.length > 0) {
                this.body = body;
                return;
            }
        }

        MultiValueMap<String, Object> multiValueMap = readMultiForm(request);
        if (CollectionUtils.isEmpty(multiValueMap)) {
            multiValueMap = readApplicationFormUrlencoded(request);
        }
        this.multiValueMap = multiValueMap;

    }

    private LinkedMultiValueMap<String, Object> readMultiForm(HttpServletRequest request) {

        if (request instanceof MultipartHttpServletRequest) {
            MultipartHttpServletRequest stand = (MultipartHttpServletRequest) request;
            MultiValueMap<String, MultipartFile> multipartFiles = stand.getMultiFileMap();
            LinkedMultiValueMap<String, Object> map = readFormData(request);
            multipartFiles.forEach((name, partFile) -> {
                MultipartFile file = partFile.get(0);
                try {
                    map.add(name, new MultipartInputStreamFileResource(file.getInputStream(), file.getOriginalFilename()));
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }

            });
            return map;
        }
        return null;
    }

    private LinkedMultiValueMap<String, Object> readApplicationFormUrlencoded(HttpServletRequest request) {
        if (!CollectionUtils.isEmpty(request.getParameterMap())) {
            return readFormData(request);
        }
        return null;
    }

    private LinkedMultiValueMap<String, Object> readFormData(HttpServletRequest request) {
        LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        Enumeration<String> names = request.getParameterNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            map.put(name, Collections.singletonList(request.getParameter(name)));
        }
        return map;

    }

    private byte[] readBodyToByteArray(HttpServletRequest request) {
        try {
            return IOUtils.toByteArray(request.getInputStream());
        } catch (IOException e) {
            log.error("read request inputStream error {}", e.getMessage(), e);
        }
        return new byte[0];
    }


    public static class MultipartInputStreamFileResource extends InputStreamResource {

        private final String filename;

        MultipartInputStreamFileResource(InputStream inputStream, String filename) {
            super(inputStream);
            this.filename = filename;
        }

        @Override
        public String getFilename() {
            return this.filename;
        }

        @Override
        public long contentLength() throws IOException {
            // we do not want to generally read the whole stream into memory ...
            return -1;
        }
    }

}
