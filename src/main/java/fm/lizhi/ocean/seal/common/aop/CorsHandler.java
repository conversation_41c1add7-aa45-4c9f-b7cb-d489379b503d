package fm.lizhi.ocean.seal.common.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;

@Slf4j
@Aspect
@Component
public class CorsHandler {

	@AfterReturning(value = "@annotation(fm.lizhi.ocean.seal.common.aop.Cors)")
	public void addResponseHeader() throws Throwable {
		RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
		ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) requestAttributes;
		HttpServletResponse response = servletRequestAttributes.getResponse();
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Headers", "*");
		log.info("addResponseHeader请求结束");
	}

}
