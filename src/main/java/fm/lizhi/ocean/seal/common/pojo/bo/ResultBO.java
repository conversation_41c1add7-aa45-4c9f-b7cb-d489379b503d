package fm.lizhi.ocean.seal.common.pojo.bo;

import fm.lizhi.ocean.seal.common.pojo.vo.ResultVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.beans.Transient;

@Data
@Accessors(chain = true)
public class ResultBO<T> {

    public static final int DEFAULT_SUCCESS = 0;

    public static final int DEFAULT_ERROR = -10001;

    private int code;

    private String msg;

    private T data;

    public static <T> ResultBO<T> resultSuccess() {
        return new ResultBO<T>().setCode(DEFAULT_SUCCESS);
    }

    public static <T> ResultBO<T> resultSuccess(T data) {
        return new ResultBO<T>().setCode(DEFAULT_SUCCESS).setData(data);
    }

    public static <T> ResultBO<T> resultFailure() {
        return new ResultBO<T>().setCode(DEFAULT_ERROR);
    }

    public static <T> ResultBO<T> resultFailure(int code) {
        return new ResultBO<T>().setCode(code);
    }

    public static <T> ResultBO<T> resultFailure(String msg) {
        return new ResultBO<T>().setCode(DEFAULT_ERROR).setMsg(msg);
    }

    public static <T> ResultBO<T> resultFailure(int code, String msg) {
        return new ResultBO<T>().setCode(code).setMsg(msg);
    }

    @Transient
    public boolean isSuccess() {
        return code == DEFAULT_SUCCESS;
    }

    public ResultVO<T> toResultVO() {
        return new ResultVO<T>().setRCode(code).setMsg(msg).setData(data);
    }
}
