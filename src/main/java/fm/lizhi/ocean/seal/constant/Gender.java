package fm.lizhi.ocean.seal.constant;

import org.jetbrains.annotations.NotNull;

public enum Gender {

    /**
     * 荔枝
     */
    MALE(0, "male"),

    FEMALE(1, "female"),

    ;

    private final int code;
    private final String gender;

    Gender(int code, String gender) {
        this.code = code;
        this.gender = gender;
    }

    public int getCode() {
        return code;
    }
    public String getGender() {
        return gender;
    }

    public static Gender fromCode(int code) {
        for (Gender gender : values()) {
            if (gender.getCode()==code) {
                return gender;
            }
        }
        return null;
    }
}
