package fm.lizhi.ocean.seal.constant;

import org.jetbrains.annotations.NotNull;

import java.util.Objects;

public enum Tenant {

    /**
     * 荔枝
     */
    PIONE("pione"),

    PONG("piliao");

    private final String code;

    Tenant(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    @NotNull
    public static Tenant fromCode(String code) {
        for (Tenant tenant : values()) {
            if (Objects.equals(tenant.getCode(), code)) {
                return tenant;
            }
        }
        return null;
    }
}
