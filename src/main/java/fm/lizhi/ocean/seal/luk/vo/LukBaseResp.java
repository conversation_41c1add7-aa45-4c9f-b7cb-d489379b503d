package fm.lizhi.ocean.seal.luk.vo;

import fm.lizhi.ocean.seal.RetCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LUK基础响应体
 *
 * <AUTHOR>
 * @param <T>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LukBaseResp<T> {

    /**
     * 响应码
     */
    private int code;

    /**
     * 响应消息
     */
    private String msg;

    /**
     * 业务响应数据
     */
    private T data;

    /**
     * sdk错误码
     */
    private Integer sdk_error_code;

    public void setRetCode(RetCodeEnum retCode) {
        this.code = retCode.getIndex();
        this.msg = retCode.getName();
    }

    /**
     * 创建成功响应
     */
    public static <T> LukBaseResp<T> success(T data) {
        LukBaseResp<T> resp = new LukBaseResp<>();
        resp.setRetCode(RetCodeEnum.SUCCESS);
        resp.setData(data);
        return resp;
    }

    /**
     * 创建失败响应
     */
    public static <T> LukBaseResp<T> failure() {
        LukBaseResp<T> resp = new LukBaseResp<>();
        resp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        return resp;
    }
}
