package fm.lizhi.ocean.seal.luk.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刷新用户令牌请求
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GetChannelTokenReq {

    /**
     * 用户 ID
     */
    @JsonProperty("c_uid")
    private String uid;

    /**
     * 用户 Token
     */
    @JsonProperty("token")
    private String token;

    /**
     * 秒级时间戳
     */
    @JsonProperty("timestamp")
    private Integer timestamp;

    /**
     * 签名
     */
    private String sign;

    /**
     * 过期剩余秒数
     */
    @JsonProperty("left_time")
    private Integer leftTime;
}
