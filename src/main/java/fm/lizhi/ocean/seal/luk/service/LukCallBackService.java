package fm.lizhi.ocean.seal.luk.service;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import fm.lizhi.ocean.seal.luk.vo.LukBaseResp;
import fm.lizhi.ocean.seal.luk.vo.GetChannelTokenReq;
import fm.lizhi.ocean.seal.luk.vo.GetChannelTokenResp;
import fm.lizhi.ocean.seal.luk.vo.RefreshChannelTokenReq;
import fm.lizhi.ocean.seal.luk.vo.RefreshChannelTokenResp;
import io.github.cfgametech.sign.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * LUK回调服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LukCallBackService {

    @Resource
    private GameAuthService gameAuthService;

    @Resource
    private GameAppConfigService gameAppConfigService;

    @Autowired
    private LzConfig lzConfig;

    /**
     * 刷新用户令牌
     *
     * @param appId       APP ID
     * @param appId     应用ID
     * @return 刷新令牌响应
     */
    public LukBaseResp<GetChannelTokenResp> getChannelToken(String appId, GetChannelTokenReq reqParam) {

        LukBaseResp<GetChannelTokenResp> baseResp = new LukBaseResp<>();
        try {
            //验签
            boolean signSuccess = verifySign(appId, reqParam, reqParam.getSign());
            if (!signSuccess) {
                log.warn("luck回调验签失败，找不到luck channel配置，appId: {}", appId);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            Result<GameAuthServiceProto.ResponseGetServerToken> ssTokenResult = gameAuthService.getServerToken(reqParam.getToken(), GameChannel.LUK, appId);
            if (ssTokenResult.rCode() != 0 || ssTokenResult.target() == null
                    || ssTokenResult.target().getToken().getErrorCode() != 0) {
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            GameAuthServiceProto.ServerToken sudSSToken = ssTokenResult.target().getToken();

            // 构建响应数据
            GetChannelTokenResp respData = GetChannelTokenResp.builder()
                    .token(sudSSToken.getToken())
                    .left_time(Math.max(sudSSToken.getExpireDate(), 0)) // 确保不返回负数
                    .build();

            baseResp.setRetCode(RetCodeEnum.SUCCESS);
            baseResp.setData(respData);

            log.info("获取用户令牌成功, appId:{}, cUid:{}, , leftTime:{}", appId, reqParam.getUid(), sudSSToken.getExpireDate());

        } catch (Exception e) {
            log.error("获取用户令牌异常, appId:{}, cUid:{}", appId, reqParam.getUid(), e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        }

        return baseResp;
    }

    /**
     * 刷新用户令牌
     *
     * @param appId    应用ID
     * @param reqParam 请求参数
     * @return 刷新令牌响应
     */
    public LukBaseResp<RefreshChannelTokenResp> refreshChannelToken(String appId, RefreshChannelTokenReq reqParam) {
        LukBaseResp<RefreshChannelTokenResp> baseResp = new LukBaseResp<>();

        try {
            //验签
            boolean signSuccess = verifySign(appId, reqParam, reqParam.getSign());
            if (!signSuccess) {
                log.warn("luck回调刷新token验签失败，找不到luck channel配置，appId: {}", appId);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }

            Result<GameAuthServiceProto.ResponseUpdateServerToken> updateSSTokenResult = gameAuthService.updateServerToken(reqParam.getToken(), GameChannel.LUK, appId);
            if (updateSSTokenResult.rCode() != 0 || updateSSTokenResult.target() == null
                    || updateSSTokenResult.target().getToken().getErrorCode() != 0) {
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }
            GameAuthServiceProto.ServerToken sudSSToken = updateSSTokenResult.target().getToken();

            RefreshChannelTokenResp refreshChannelTokenResp = RefreshChannelTokenResp.builder()
                    .token(sudSSToken.getToken())
                    .left_time(Math.max(sudSSToken.getExpireDate(), 0)).build();
            log.info("刷新用户令牌成功, appId:{}, cUid:{}, , leftTime:{}", appId, reqParam.getUid(), sudSSToken.getExpireDate());
            baseResp.setRetCode(RetCodeEnum.SUCCESS);
            baseResp.setData(refreshChannelTokenResp);

        } catch (Exception e) {
            log.error("刷新用户令牌异常, appId:{}, cUid:{}", appId, reqParam.getUid(), e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        }

        return baseResp;
    }

    private boolean verifySign(String appId, Object reqParam, String sign) {

        if (!lzConfig.isLuckCheckCallbackSign()) {
            log.info("luck回调不验签，请注意");
            return true;
        }
        GameAppServiceProto.GameAppInfo gameAppInfo = gameAppConfigService.getGameAppInfo(appId);
        Optional<GameAppServiceProto.ChannelInfo> channelInfo = gameAppInfo.getChannelInfosList()
                .stream()
                .filter(channel -> Objects.equals(channel.getChannel(), GameChannel.LUK))
                .findAny();

        String channelSecret = channelInfo.map(GameAppServiceProto.ChannelInfo::getChannelAppSecret).orElse(null);
        if (channelSecret == null) {
            log.warn("luck回调获取token验签失败，找不到luck channel配置，appId: {}", appId);
            return false;
        }
        try {
            String signResult = SignUtils.signature(channelSecret, reqParam);
            if (!Objects.equals(signResult, sign)) {
                log.warn("luck回调验签失败，appId: {}", appId);
                return false;
            }
            return true;
        } catch (IllegalAccessException e) {
            log.error("luck回调验签失败，appId: {}", appId, e);
            return false;
        }
    }
}
