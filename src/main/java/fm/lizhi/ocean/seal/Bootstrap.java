package fm.lizhi.ocean.seal;

import fm.lizhi.commons.config.service.ConfigService;
import fm.lizhi.commons.initiator.Initiator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.retry.annotation.EnableRetry;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
//@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class, MongoAutoConfiguration.class,
//        MongoDataAutoConfiguration.class})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableRetry
public class Bootstrap {

    public static void main(String[] args) throws Exception {
        try {
            Initiator.init();
            SpringApplication app = new SpringApplication(Bootstrap.class);
            @SuppressWarnings("unchecked")
            Map<String, Object> springAutoConfiguration = ConfigService.loadConfig(Map.class);
            app.setDefaultProperties(springAutoConfiguration);
            app.run(args);
        } catch (Exception e) {
            log.error("启动失败", e);
            throw e;
        }
    }
}
