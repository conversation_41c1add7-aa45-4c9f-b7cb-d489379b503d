package fm.lizhi.ocean.seal.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.common.pojo.bo.ResultBO;
import fm.lizhi.ocean.seal.common.pojo.bo.UserInfo;
import fm.lizhi.ocean.seal.constant.GameCallbackType;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import fm.lizhi.ocean.seal.service.UserService;
import fm.lizhi.ocean.seal.util.SealSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务
 * <p>
 * Created in 2022-04-18 15:45.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    private static final String KEY_SPLIT = "__________";

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private GameAppConfigService gameAppConfigService;
    @Resource
    private UserServiceImpl userServiceImpl;

    // 用户缓存
    @SuppressWarnings("all")
    private LoadingCache<String, UserInfo> userCache = CacheBuilder.newBuilder()
            // 3分钟用户信息缓存
            .expireAfterWrite(3, TimeUnit.MINUTES)
            // 缓存2000个用户
            .maximumSize(2000)
            .build(new CacheLoader<String, UserInfo>() {
                @Override
                public UserInfo load(String key) throws Exception {
                    String[] split = key.split(KEY_SPLIT);
                    String appId = split[0];
                    long userId = Long.parseLong(split[1]);
                    return loadUserInfo(appId, userId);
                }
            });


    /**
     * 获取业务方用户信息
     *
     * @param appId  AppID
     * @param userId 用户ID
     * @return
     */
    @Override
    public ResultBO<UserInfo> getUserInfoFromCache(String appId, long userId) {
        ResultBO<UserInfo> resultBO = ResultBO.resultSuccess();
        try {
            UserInfo userInfo = this.userCache.getUnchecked(appId + KEY_SPLIT + userId);
            resultBO.setData(userInfo);
        } catch (Exception e) {
            logger.error("exception get user info, appId:{}, userId:{}, msg:{}", appId, userId, e.getMessage(), e);
            resultBO.setCode(RetCodeEnum.REQUEST_FAILED.getIndex());
        }
        return resultBO;
    }

    /**
     * 获取业务方用户信息
     *
     * @param appId  appId
     * @param userId 用户ID
     * @return
     */
    @Override
    public ResultBO<UserInfo> getUserInfo(String appId, long userId) {
        ResultBO<UserInfo> resultBO = ResultBO.resultSuccess();
        try {
            UserInfo userInfo = this.loadUserInfo(appId, userId);
            resultBO.setData(userInfo);
        } catch (Exception e) {
            logger.error("exception get user info, appId:{}, userId:{}, msg:{}", appId, userId, e.getMessage(), e);
            resultBO.setCode(RetCodeEnum.REQUEST_FAILED.getIndex());
        }
        return resultBO;
    }

    /**
     * 加载用户信息
     *
     * @param appId  appId
     * @param userId 用户ID
     * @return
     */
    private UserInfo loadUserInfo(String appId, long userId) {
        Optional<GameAppServiceProto.AppCallback> appCallbackOptional = gameAppConfigService.getGameAppCallbackConfig(appId, GameCallbackType.GET_USER);

        if (!appCallbackOptional.isPresent()) {
            throw new RuntimeException("failed to get user info from business, url is empty.");
        }

        GameAppServiceProto.AppCallback appCallback = appCallbackOptional.get();

        String key = appCallback.getCallbackKey();
        String query = appCallback.getCallbackGetUserUrl() + "?userId=" + userId;

        HttpHeaders httpHeaders = new HttpHeaders();
        // 增加seal验签参数
        SealSignUtils.addSignHeaders(httpHeaders, appId, key);

        ResponseEntity<JSONObject> resp;
        try {
            resp = userServiceImpl.doHttpRequest(httpHeaders, query);
        } catch (Exception e) {
            log.error("http get user info from business error: {}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }
        if (!Objects.equals(resp.getStatusCodeValue(), HttpStatus.OK.value())) {
            log.error("http get user info from business httpStatus errorCode: {}", resp.getStatusCodeValue());
            throw new RuntimeException("business error");
        }

        JSONObject jsonObject = resp.getBody();
        logger.info("get user info from business, appId:{}, userId:{}, result:{}", appId, userId, jsonObject);
        if (jsonObject == null || jsonObject.getInteger("rCode") == null || jsonObject.getInteger("rCode") != 0) {
            logger.error("failed to get user info from business, appId:{}, userId:{}, result:{}", appId, userId, jsonObject);
            throw new RuntimeException("failed to get user info from business");
        }
        UserInfo userInfo = jsonObject.getObject("data", UserInfo.class);
        if (userInfo == null) {
            logger.error("failed to get user info from business, data is null, appId:{}, userId:{}, json:{}", appId, userId, jsonObject);
        }
        return userInfo;
    }

    @Retryable(
            value = {java.net.ConnectException.class, java.net.SocketTimeoutException.class},
            maxAttempts = 2,
            backoff = @Backoff(value = 0, delay = 0, multiplier = 0)
    )
    public ResponseEntity<JSONObject> doHttpRequest(HttpHeaders httpHeaders, String url) {
        return restTemplate.exchange(url, HttpMethod.GET, new HttpEntity<>(httpHeaders), JSONObject.class);
    }
}