package fm.lizhi.ocean.seal.service.impl;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.seal.common.pojo.vo.HttpForwardRequestContext;
import fm.lizhi.ocean.seal.common.pojo.vo.HttpForwardResult;
import fm.lizhi.ocean.seal.constant.GameCallbackType;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import fm.lizhi.ocean.seal.service.HttpForwardService;
import fm.lizhi.ocean.seal.util.HttpParameterUtils;
import fm.lizhi.ocean.seal.util.SealSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HttpForwardServiceImpl implements HttpForwardService {

    protected static final String DEFAULT_BODY_CHARSET = "UTF-8";

    @Resource
    private GameAppConfigService gameAppConfigService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private HttpForwardServiceImpl httpForwardServiceImpl;

    @Override
    public HttpForwardResult doDispatch(@NotNull String appId, @NotNull HttpServletRequest httpServletRequest, Object body) {
        printSourceLog(httpServletRequest, body);
        // 找到目标转发地址
        Optional<GameAppServiceProto.AppCallback> gameAppCallbackConfigOptional = gameAppConfigService.getGameAppCallbackConfig(appId, GameCallbackType.THIRD_PARTY_FORWARD);

        if (!gameAppCallbackConfigOptional.isPresent()) {
            // 为空说明没有配置回调
            log.info("appId：{} 没有配置转发地址，忽略", appId);
            return HttpForwardResult.success();
        }

        GameAppServiceProto.AppCallback gameAppCallbackConfig = gameAppCallbackConfigOptional.get();
        // 接口字段命名有歧义，先这样吧
        String businessCallbackUrl = gameAppCallbackConfig.getCallbackGetUserUrl();
        String callbackKey = gameAppCallbackConfig.getCallbackKey();

        HttpHeaders httpHeaders = HttpParameterUtils.extractHttpHeaders(httpServletRequest);

        // 增加seal验签参数
        SealSignUtils.addSignHeaders(httpHeaders, appId, callbackKey);

        // 处理参数
        HttpForwardRequestContext context = HttpForwardRequestContext.builder()
                .headers(httpHeaders)
                .requestPath(businessCallbackUrl)
                .build();

        context.parseRequest(httpServletRequest);

        log.info("回调业务,appId: {} 接口地址：{}", appId, businessCallbackUrl);

        ResponseEntity<byte[]> resp;
        try {
            resp = httpForwardServiceImpl.doInvoke(context);
        } catch (Exception e) {
            log.error("http proxy request error: {}", e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }

        int statusCodeValue = resp.getStatusCodeValue();

        String respBody = getStrRespBody(resp);

        log.info("http proxy response url {}, header {}, body {}",
                context.getRequestPath(),
                JSON.toJSONString(resp.getHeaders()),
                respBody);

        return new HttpForwardResult(statusCodeValue, respBody);
    }

    private String getStrRespBody(ResponseEntity<byte[]> resp) {
        // 处理响应
        String respBody = null;
        if (resp.getBody() != null) {
            try {
                respBody = IOUtils.toString(resp.getBody(), DEFAULT_BODY_CHARSET);
            } catch (IOException e) {
                log.error("解析转发响应body失败：{}", e.getMessage());
                log.error(e.getMessage(), e);
            }
        }
        return respBody;
    }

    @Retryable(
            value = {java.net.ConnectException.class, java.net.SocketTimeoutException.class},
            maxAttempts = 2,
            backoff = @Backoff(value = 0, delay = 0, multiplier = 0)
    )
    public ResponseEntity<byte[]> doInvoke(HttpForwardRequestContext context) {
        // 实际请求
        HttpEntity<?> entity = context.toHttpEntity();
        return restTemplate.exchange(context.getRequestPath(), context.getMethod(), entity, byte[].class);
    }


    private void printSourceLog(HttpServletRequest request, Object body) {
        log.info("========== http forward log start ==========");
        log.info("method: {}", request.getMethod());
        log.info("requestURI: {}", request.getRequestURI());
        if (Objects.nonNull(body)) {
            log.info("body: {}", JSON.toJSONString(body));
        }
        log.info("========== http forward log end ==========");

    }
}
