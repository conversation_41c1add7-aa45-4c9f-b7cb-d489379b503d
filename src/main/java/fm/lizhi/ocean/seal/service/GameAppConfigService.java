package fm.lizhi.ocean.seal.service;

import fm.lizhi.ocean.seal.constant.GameCallbackType;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface GameAppConfigService {

    Optional<GameAppServiceProto.AppCallback> getGameAppCallbackConfig(String appId, GameCallbackType callbackType);

    GameAppServiceProto.GameAppInfo getGameAppInfo(String appId);
}
