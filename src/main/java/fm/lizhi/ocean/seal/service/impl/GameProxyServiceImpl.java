//package fm.lizhi.ocean.seal.service.impl;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import fm.lizhi.commons.service.client.pojo.Result;
//import fm.lizhi.commons.service.client.rcode.GeneralRCode;
//import fm.lizhi.ocean.seal.conf.LzConfig;
//import fm.lizhi.ocean.seal.lizhi.bo.AppIdAppKey;
//import fm.lizhi.ocean.seal.lizhi.vo.ProxyResult;
//import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseGetAppIdAppSecretByParam;
//import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.GetAppIdAppSecretParam;
//import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.InvokeTargetParams;
//import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto.ResponseInvokeTarget;
//import fm.lizhi.ocean.seal.service.GameProxyService;
//import org.apache.commons.codec.digest.DigestUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.Map;
//import java.util.Set;
//import java.util.TreeMap;
//
///**
// * Created in 2022-06-23 16:02.
// *
// * <AUTHOR>
// */
//@Service("gameProxyServiceImpl")
//public class GameProxyServiceImpl implements GameProxyService {
//    private static final Logger logger = LoggerFactory.getLogger(GameProxyServiceImpl.class);
//    @Resource
//    private fm.lizhi.ocean.seal.api.GameProxyService gameProxyService;
//    @Resource
//    private LzConfig lzConfig;
//
//    /**
//     * 调用目标方法
//     *
//     * @param appId         应用ID
//     * @param gameId        游戏ID
//     * @param type          类型
//     * @param interfaceAddr 接口地址
//     * @param params        参数
//     * @return
//     */
//    @Override
//    public ProxyResult invokeTarget(String appId, String gameId, int type, String interfaceAddr, String params) {
//        ProxyResult response = new ProxyResult();
//        InvokeTargetParams.Builder builder = InvokeTargetParams.newBuilder();
//        builder.setAppId(appId)
//                .setGameId(gameId)
//                .setType(type)
//                .setInterface(interfaceAddr)
//                .setParams(params);
//        Result<ResponseInvokeTarget> result = this.gameProxyService.invokeTarget(builder.build());
//        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
//            response.setCode(result.rCode());
//            response.setMsg("target service error");
//            logger.error("Failed to invoke target, target service error, code:{}, appId:{}, gameId:{}, type:{}, interface:{}", result.rCode(), appId, gameId, type, interfaceAddr);
//        } else {
//            ResponseInvokeTarget target = result.target();
//            response.setCode(result.rCode());
//            response.setBizCode(result.target().getBizCode());
//            response.setMsg(target.getMsg());
//            response.setData(target.getData());
//        }
//        return response;
//    }
//
//    /**
//     * 参数签名
//     *
//     * @param params      请求参数
//     * @param appIdAppKey 渠道AppId与渠道appKey
//     * @return
//     */
//    @Override
//    public String sign(JSONObject params, AppIdAppKey appIdAppKey) {
//        TreeMap<String, Object> map = new TreeMap<>();
//        Set<Map.Entry<String, Object>> entries = params.entrySet();
//        for (Map.Entry<String, Object> entry : entries) {
//            String key = entry.getKey();
//            Object value = entry.getValue();
//            map.put(key, value);
//        }
//
//        StringBuilder sb = new StringBuilder();
//        for (Map.Entry<String, Object> entry : map.entrySet()) {
//            sb.append(entry.getKey());
//            sb.append("=");
//            sb.append(entry.getValue());
//            sb.append("&");
//        }
//        sb.append(appIdAppKey.getAppId()).append("&").append(appIdAppKey.getAppKey());
//        return DigestUtils.md5Hex(sb.toString());
//    }
//
//    /**
//     * 获取渠道appId与appKey
//     * @param sealAppId 业务的appId,不是游戏渠道的appId
//     * @param gameId 游戏gameId.这里可能是渠道的游戏Id,也可能是seal平台的gameId。使用type进行区分
//     * @param type 1 gameId标志为seal平台的gameId。这个接口为获取业务的appId 与 appSecret
//     *             2 gameId为渠道游戏的gameId，这个接口为获取渠道的appId 与 appSecret
//     * @return
//     */
//    @Override
//    public AppIdAppKey getChannelAppIdAndAppKey(String sealAppId, String gameId, int type) {
//        Result<ResponseGetAppIdAppSecretByParam> result = gameProxyService.getAppIdAppSecretByParam(
//                GetAppIdAppSecretParam.newBuilder().setAppId(sealAppId).setGameId(gameId).setType(type).build());
//        if(result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS){
//            logger.error("getChannelAppIdAndAppKey error.`sealAppId={}`gameId={}`type={}`rCode={}"
//                    , sealAppId, gameId, type, result.rCode());
//            return null;
//        }
//
//        AppIdAppKey appIdAppKey = new AppIdAppKey();
//        appIdAppKey.setAppId(result.target().getAppId());
//        appIdAppKey.setAppKey(result.target().getAppSecret());
//        return appIdAppKey;
//    }
//}