package fm.lizhi.ocean.seal.service;

import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.seal.lizhi.bo.AppIdAppKey;
import fm.lizhi.ocean.seal.lizhi.vo.ProxyResult;

/**
 * Created in 2022-06-23 16:01.
 *
 * <AUTHOR>
 */
public interface GameProxyService {
    /**
     * 调用目标方法
     *
     * @param appId         应用ID
     * @param gameId        游戏ID
     * @param type          类型
     * @param interfaceAddr 接口地址
     * @param params        参数
     * @return
     */
    ProxyResult invokeTarget(String appId, String gameId, int type, String interfaceAddr, String params);

    /**
     * 参数签名
     *
     * @param params      请求参数
     * @param appIdAppKey 渠道appId与appKey
     * @return
     */
    String sign(JSONObject params, AppIdAppKey appIdAppKey);

    /**
     * 获取渠道appId与appKey
     * @param sealAppId 业务的appId,不是游戏渠道的appId
     * @param gameId 游戏gameId.这里可能是渠道的游戏Id,也可能是seal平台的gameId。使用type进行区分
     * @param type 1 gameId标志为seal平台的gameId。这个接口为获取业务的appId 与 appSecret
     *             2 gameId为渠道游戏的gameId，这个接口为获取渠道的appId 与 appSecret
     * @return
     */
    AppIdAppKey getChannelAppIdAndAppKey(String sealAppId, String gameId, int type);
}
