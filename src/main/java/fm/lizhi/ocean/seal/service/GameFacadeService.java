package fm.lizhi.ocean.seal.service;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameService;
import fm.lizhi.ocean.seal.protocol.GameServiceProto;
import fm.lizhi.ocean.seal.websocket.dto.BizResult;
import fm.lizhi.ocean.seal.websocket.dto.GameConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 针对于GameService的封装
 * <p>
 * Created in 2022-05-11 17:23.
 *
 * <AUTHOR>
 */
@Service
public class GameFacadeService {
    private static final Logger logger = LoggerFactory.getLogger(GameFacadeService.class);
    @Resource
    private GameService gameService;

    /**
     * 获取游戏详情
     *
     * @param appId  业务appId
     * @param gameId 游戏id，可能是平台游戏ID，可能是渠道游戏Id
     * @return
     */
    public BizResult<GameServiceProto.GameDetail> getGameDetail(String appId, String gameId) {
        BizResult<GameServiceProto.GameDetail> bizResult = BizResult.success();
        GameServiceProto.GetGameDetailParam param = GameServiceProto.GetGameDetailParam.newBuilder()
                .setAppId(appId)
                .setGameId(gameId)
                .build();
        Result<GameServiceProto.ResponseGetGameDetail> result = this.gameService.getGameDetail(param);
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            logger.error("Get game detail error, appId:{}, gameId:{}, rCode:{}", appId, gameId, result.rCode());
            bizResult.setCode(result.rCode());
            bizResult.setMsg("operate failed");
            return bizResult;
        }
        bizResult.setData(result.target().getGameDetail());
        return bizResult;
    }

    /**
     * 获取游戏配置信息
     *
     * @param appId  业务appId
     * @param gameId 游戏id，可能是平台游戏ID，可能是渠道游戏Id
     * @return
     */
    public BizResult<GameConfig> getGameConfig(String appId, String gameId) {
        BizResult<GameConfig> result = BizResult.success();
        BizResult<GameServiceProto.GameDetail> bizResult = this.getGameDetail(appId, gameId);
        if (bizResult.isNotSuccess()) {
            result.setCode(bizResult.getCode());
            result.setMsg(bizResult.getMsg());
            return result;
        }
        GameConfig config = JSON.parseObject(bizResult.getData().getConfig(), GameConfig.class);
        config.setCaptain(bizResult.getData().getCaptain());
        result.setData(config);
        return result;
    }
}