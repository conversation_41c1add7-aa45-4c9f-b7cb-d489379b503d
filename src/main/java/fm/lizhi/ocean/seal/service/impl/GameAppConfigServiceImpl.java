package fm.lizhi.ocean.seal.service.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.api.GameAppService;
import fm.lizhi.ocean.seal.common.pojo.vo.CallbackCacheKey;
import fm.lizhi.ocean.seal.constant.GameCallbackType;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GameAppConfigServiceImpl implements GameAppConfigService {

    @Resource
    private GameAppService gameAppService;

    // 应用回调配置缓存
    @SuppressWarnings("all")
    private final LoadingCache<CallbackCacheKey, Optional<GameAppServiceProto.AppCallback>> gameAppCallbackCache = CacheBuilder.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(100)
            .build(new CacheLoader<CallbackCacheKey, Optional<GameAppServiceProto.AppCallback>>() {
                @Override
                public Optional<GameAppServiceProto.AppCallback> load(CallbackCacheKey key) throws Exception {
                    return loadAppCallback(key);
                }
            });

    // 应用信息缓存
    @SuppressWarnings("all")
    private final LoadingCache<String, Optional<GameAppServiceProto.GameAppInfo>> gameAppInfoCache = CacheBuilder.newBuilder()
            .maximumSize(100).expireAfterWrite(60, TimeUnit.MINUTES)
            .build(new CacheLoader<String, Optional<GameAppServiceProto.GameAppInfo>>() {
                @Override
                public Optional<GameAppServiceProto.GameAppInfo> load(String key) {
                    GameAppServiceProto.GameAppInfo gameAppInfo = loadGameAppInfo(key);
                    if (gameAppInfo == null) {
                        return Optional.empty();
                    }
                    return Optional.of(gameAppInfo);
                }
            });

    @Override
    public Optional<GameAppServiceProto.AppCallback> getGameAppCallbackConfig(String appId, GameCallbackType callbackType) {
        return gameAppCallbackCache.getUnchecked(new CallbackCacheKey(appId, callbackType.getValue()));
    }

    @Override
    public GameAppServiceProto.GameAppInfo getGameAppInfo(String appId) {
        Optional<GameAppServiceProto.GameAppInfo> optional = gameAppInfoCache.getUnchecked(appId);
        if (!optional.isPresent()) {
            throw new RuntimeException("failed to get app info");
        }
        return optional.get();
    }

    /**
     * 加载app回调配置信息
     */
    private Optional<GameAppServiceProto.AppCallback> loadAppCallback(CallbackCacheKey callbackCacheKey) {
        // 获取对应AppID的配置信息
        Result<GameAppServiceProto.ResponseGetAppCallback> result = gameAppService.getAppCallback(
                GameAppServiceProto.GetAppCallbackParam.newBuilder()
                        .setAppId(callbackCacheKey.getAppId())
                        .setType(callbackCacheKey.getCallbackType())
                        .build());
        if (result.rCode() != 0) {
            // logger.error("failed to get app callback, appId:{}, rCode:{}", appId, result.rCode());
            return Optional.empty();
        }
        return Optional.of(result.target().getAppConfig());
    }

    private GameAppServiceProto.GameAppInfo loadGameAppInfo(String appId) {
        try {
            Result<GameAppServiceProto.ResponseGetAppInfoByAppId> result = gameAppService.getAppInfoByAppId(appId);
            if (result.rCode() == GeneralRCode.GENERAL_RCODE_SUCCESS) {
                return result.target().getGameAppInfo();
            }
            log.warn("load game app info error.`appId={}`rCode={}", appId, result.rCode());
        } catch (Exception e) {
            log.error("load game app info error.`appId={}", appId, e);
        }
        return null;
    }
}
