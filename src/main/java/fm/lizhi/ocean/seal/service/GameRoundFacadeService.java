package fm.lizhi.ocean.seal.service;

import fm.lizhi.ocean.seal.api.GameRoundService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created in 2022-05-23 14:53.
 *
 * <AUTHOR>
 */
@Service
public class GameRoundFacadeService {
    private static final Logger logger = LoggerFactory.getLogger(GameRoundFacadeService.class);
    @Resource
    private GameRoundService gameRoundService;
}