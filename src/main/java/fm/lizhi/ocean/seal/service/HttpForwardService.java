package fm.lizhi.ocean.seal.service;

import fm.lizhi.ocean.seal.common.pojo.vo.HttpForwardResult;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

/**
 * http转发服务，用于如第三方回调，验签后透传到业务侧，本服务只做中间层验签和加密等
 *
 * <AUTHOR>
 */
public interface HttpForwardService {

    /**
     * 处理转发
     */
    HttpForwardResult doDispatch(@NotNull String appId, @NotNull HttpServletRequest request, Object body);
}
