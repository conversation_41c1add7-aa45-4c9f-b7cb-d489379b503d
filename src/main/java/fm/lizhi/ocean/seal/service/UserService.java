package fm.lizhi.ocean.seal.service;

import fm.lizhi.ocean.seal.common.pojo.bo.ResultBO;
import fm.lizhi.ocean.seal.common.pojo.bo.UserInfo;

/**
 * Created in 2022-04-18 15:45.
 *
 * <AUTHOR>
 */
public interface UserService {

    /**
     * 获取业务方用户信息
     *
     * @param appId  AppID
     * @param userId 用户ID
     * @return
     */
    ResultBO<UserInfo> getUserInfoFromCache(String appId, long userId);

    /**
     * 获取业务方用户信息
     *
     * @param appId  appId
     * @param userId 用户ID
     * @return
     */
    ResultBO<UserInfo> getUserInfo(String appId, long userId);
}
