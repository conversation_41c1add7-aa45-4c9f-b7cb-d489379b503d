package fm.lizhi.ocean.seal.sud.service;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.common.pojo.bo.ResultBO;
import fm.lizhi.ocean.seal.common.pojo.bo.UserInfo;
import fm.lizhi.ocean.seal.common.pojo.vo.HttpForwardResult;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.Gender;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import fm.lizhi.ocean.seal.service.HttpForwardService;
import fm.lizhi.ocean.seal.service.UserService;
import fm.lizhi.ocean.seal.singleton.NotifyEventHandlerFactory;
import fm.lizhi.ocean.seal.sud.bo.GameResult;
import fm.lizhi.ocean.seal.sud.bo.RoomUsersChangedModel;
import fm.lizhi.ocean.seal.sud.vo.*;
import fm.lizhi.ocean.seal.util.SudSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Objects;
import java.util.Optional;

/**
 * sud call back Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SudCallBackService {
    private static final Logger logger = LoggerFactory.getLogger(SudCallBackService.class);
    @Resource
    private GameAuthService gameAuthService;
    @Autowired
    private LzConfig lzConfig;
    @Resource
    private GameReportService gameReportService;
    @Resource
    private UserService userService;
    @Resource
    private HttpForwardService httpForwardService;
    @Resource
    private GameAppConfigService gameAppConfigService;
    @Resource
    private NotifyEventHandlerFactory notifyEventHandlerFactory;

    /**
     * 获取长期令牌SSToken(根据短期令牌Code更换长期令牌SSToken)
     *
     * @param code 短期令牌
     * @return 长期令牌SSToken响应
     */
    public BaseResp<GetSSTokenResp> getSSToken(String code, String appId) {

        // 验签
        BaseResp<GetSSTokenResp> verifyResult = sudVerifyCallbackSign(appId);
        if (verifyResult != null) {
            return verifyResult;
        }

        BaseResp<GetSSTokenResp> baseResp = new BaseResp<>();
        Result<GameAuthServiceProto.ResponseGetServerToken> ssTokenResult = gameAuthService.getServerToken(code, GameChannel.SUD, appId);
        if (ssTokenResult.rCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        GameAuthServiceProto.ServerToken sudSSToken = ssTokenResult.target().getToken();
        if (sudSSToken.getErrorCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdk_error_code(sudSSToken.getErrorCode());
            return baseResp;
        }

        String uid = sudSSToken.getUid();

        // 拿uid去查用户信息
        // 改造成http调用？http://yapi.feoffice.lizhi.fm/project/1824/interface/api/49964

        ResultBO<UserInfo> resultBO = userService.getUserInfoFromCache(appId, Long.parseLong(uid));

        if (!resultBO.isSuccess()) {
            logger.error("failed to get user info, appId:{}, userId:{}", appId, uid);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }

        UserInfo userInfo = resultBO.getData();
        Gender rawGender = Gender.fromCode(userInfo.getGender());

        GetUserInfoResp userInfoResp = GetUserInfoResp.builder()
                .uid(uid)
                .nick_name(userInfo.getName())
                .gender(rawGender == null ? "" : rawGender.getGender())
                .avatar_url(userInfo.getPortrait())
                .build();


        baseResp.setData(GetSSTokenResp.builder()
                .ss_token(sudSSToken.getToken())
                .expire_date(sudSSToken.getExpireDate())
                .user_info(userInfoResp)
                .build());
        return baseResp;
    }

    public BaseResp<UpdateSSTokenResp> updateSSToken(String ssToken, String appId) {

        // 验签
        BaseResp<UpdateSSTokenResp> verifyResult = sudVerifyCallbackSign(appId);
        if (verifyResult != null) {
            return verifyResult;
        }

        BaseResp<UpdateSSTokenResp> baseResp = new BaseResp<>();
        Result<GameAuthServiceProto.ResponseUpdateServerToken> updateSSTokenResult = gameAuthService.updateServerToken(ssToken, GameChannel.SUD, appId);
        if (updateSSTokenResult.rCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        GameAuthServiceProto.ServerToken sudSSToken = updateSSTokenResult.target().getToken();
        if (sudSSToken.getErrorCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdk_error_code(sudSSToken.getErrorCode());
            return baseResp;
        }
        baseResp.setData(UpdateSSTokenResp.builder()
                .ss_token(sudSSToken.getToken())
                .expire_date(sudSSToken.getExpireDate())
                .build());
        return baseResp;
    }

    /**
     * 20250227 新增v2版本的接口，删减了获取用户信息的逻辑（后期可能需要加上）
     */
    public BaseResp<GetUserInfoResp> getUserInfoV2(String ssToken, String appId) {

        // 验签
        BaseResp<GetUserInfoResp> verifyResult = sudVerifyCallbackSign(appId);
        if (verifyResult != null) {
            return verifyResult;
        }

        BaseResp<GetUserInfoResp> baseResp = new BaseResp<>();
        Result<GameAuthServiceProto.ResponseGetUserByServerToken> uidBySSTokenResult = gameAuthService.getUserByServerToken(ssToken, GameChannel.SUD, appId);
        if (uidBySSTokenResult.rCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        GameAuthServiceProto.GameUser gameUser = uidBySSTokenResult.target().getGameUser();
        if (gameUser.getErrorCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdk_error_code(gameUser.getErrorCode());
            return baseResp;
        }
        // 这里userId和GameUserId应该是一样的，因为dc里面只是强转为Long
        long userId = gameUser.getUserId();

        baseResp.setData(GetUserInfoResp.builder()
                .uid(gameUser.getGameUserId())
                .nick_name("未知")
                .gender("")
                .avatar_url("")
                .build());
        return baseResp;
    }

    public BaseResp<GetUserInfoResp> getUserInfo(String ssToken, String appId) {
        BaseResp<GetUserInfoResp> baseResp = new BaseResp<>();
        Result<GameAuthServiceProto.ResponseGetUserByServerToken> uidBySSTokenResult = gameAuthService.getUserByServerToken(ssToken, GameChannel.SUD, appId);
        if (uidBySSTokenResult.rCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        GameAuthServiceProto.GameUser gameUser = uidBySSTokenResult.target().getGameUser();
        if (gameUser.getErrorCode() != 0) {
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdk_error_code(gameUser.getErrorCode());
            return baseResp;
        }
        long userId = gameUser.getUserId();
        // 数据上报
        reportGetUserData(userId, gameUser.getAppId());
        logger.info("get user info from callback, appId:{}, userId:{}", appId, userId);

        ResultBO<UserInfo> resultBO = userService.getUserInfoFromCache(appId, userId);

        if (!resultBO.isSuccess()) {
            logger.error("failed to get user info, appId:{}, userId:{}", appId, userId);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        UserInfo userInfo = resultBO.getData();
        Gender rawGender = Gender.fromCode(userInfo.getGender());
        baseResp.setData(GetUserInfoResp.builder()
                .uid(gameUser.getGameUserId())
                .nick_name(userInfo.getName())
                .gender(rawGender.getGender())
                .avatar_url(userInfo.getPortrait())
                .build());
        return baseResp;
    }

    public BaseResp<Void> reportGameInfo(ReportGameInfoReq reqParam, String appId) {
        BaseResp<Void> baseResp = new BaseResp<>();
        // 权限校验
        BaseResp<Void> verifyResult = sudVerifyCallbackSign(appId);
        if (verifyResult != null) {
            return verifyResult;
        }

        GameResult gameResult = reqParam.getReport_msg();
        Result<Void> result = null;
        if (reqParam.checkGameStart()) {
            result = gameReportService.gameStartReport(gameResult.buildGameStartResult(appId, GameChannel.SUD));
        } else if (reqParam.checkGameSettle()) {
            result = gameReportService.gameSettleReport(gameResult.buildGameSettleResult(appId, GameChannel.SUD));
        }
        if (result == null) {
            log.error("gameReport fail,case result=null.param={}", reqParam.toString());
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        log.info("reportGameInfo.reqParam={}", reqParam.toString());
        if (result.rCode() != 0) {
            log.error("gameReport fail.rCode={}", result.rCode());
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        baseResp.setRetCode(RetCodeEnum.SUCCESS);
        return baseResp;
    }


    /**
     * http转发
     */
    public BaseResp<Void> reportGameInfoHttpForward(ReportGameInfoReq reqParam, String appId) {

        BaseResp<Void> baseResp = new BaseResp<>();

        // 验签
        BaseResp<Void> verifyResult = sudVerifyCallbackSign(appId);
        if (verifyResult != null) {
            return verifyResult;
        }


        // 入库？TODO

        try {
            ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = servletRequestAttributes.getRequest();

            HttpForwardResult httpForwardResult = httpForwardService.doDispatch(appId, request, reqParam);
            int statusCodeValue = httpForwardResult.getHttpStatus();

            if (!Objects.equals(statusCodeValue, HttpStatus.OK.value())) {
                log.warn("请求业务回调接口，返回状态码非200,状态码：{}", statusCodeValue);
                throw new RuntimeException("business error");
            }
        } catch (Exception e) {
            log.error("reportGameInfo forward fail. msg={}", e.getMessage());
            log.warn(e.getMessage(), e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdk_error_code(RetCodeEnum.REQUEST_FAILED.getIndex());
            return baseResp;
        }
        baseResp.setRetCode(RetCodeEnum.SUCCESS);
        return baseResp;
    }

    private <T> BaseResp<T> sudVerifyCallbackSign(String appId) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        boolean signSuccess = false;

        if (!lzConfig.isSudCheckCallbackSign()) {
            log.info("sud回调不验签，请注意");
            return null;
        }

        try {
            GameAppServiceProto.GameAppInfo gameAppInfo = gameAppConfigService.getGameAppInfo(appId);
            Optional<GameAppServiceProto.ChannelInfo> channelInfo = gameAppInfo.getChannelInfosList()
                    .stream()
                    .filter(channel -> Objects.equals(channel.getChannel(), GameChannel.SUD))
                    .findAny();

            if (channelInfo.isPresent()) {
                signSuccess = SudSignUtils.verifySignature(request, channelInfo.get().getChannelAppSecret());
            } else {
                log.error("找不到sud channel配置，appId: {}", appId);
            }

        } catch (IOException e) {
            log.error("reportGameInfo check Token fail. {}", e.getMessage());
        }
        if (!signSuccess) {
            log.error("reportGameInfo check Token fail.");
            BaseResp<T> baseResp = new BaseResp<>();
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdk_error_code(RetCodeEnum.REQUEST_FAILED.getIndex());
            return baseResp;
        }
        return null;
    }

    /**
     * 数据上报
     */
    private void reportGetUserData(long userId, String appId) {
//        try {
//            long now = System.currentTimeMillis();
//            ReportGetUserDataDOC reportGetUserDataDOC = new ReportGetUserDataDOC();
//            reportGetUserDataDOC.setUserId(userId);
//            reportGetUserDataDOC.setEventName("gameLoading");
//            reportGetUserDataDOC.setChannel(GameChannel.SUD);
//            reportGetUserDataDOC.setAppId(appId);
//            reportGetUserDataDOC.setCreateTime(now);
//            reportGetUserDataMongoDao.insertReportGetUserDataMongo(reportGetUserDataDOC);
//        } catch (Exception e) {
//            log.warn("reportGetUserData fail. userId={}`appId={}", userId, appId, e);
//        }
    }

    public BaseResp<Void> notifyEvent(String reqParam) {
        BaseResp<Void> baseResp = new BaseResp<>();
        String appId = null;
        String notifyEvent = null;
        JsonObject reqJsonObject = null;
        try {
            // 解析
            reqJsonObject = JsonParser.parseString(reqParam).getAsJsonObject();
            appId = reqJsonObject.get("app_id").getAsString();
            notifyEvent = reqJsonObject.get("notify_event").getAsString();
        } catch (Exception e) {
            log.error("notifyEvent parse error reqParam={}", reqParam, e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }

        // 权限校验
        BaseResp<Void> verifyResult = sudVerifyCallbackSign(appId);
        if (verifyResult != null) {
            return verifyResult;
        }

        try {
            SudNotifyEventHandler handler = notifyEventHandlerFactory.getHandler(notifyEvent);
            Result<Void> voidResult = handler.handleNotifyEvent(reqJsonObject, appId);
            if (voidResult == null) {
                log.error("notifyEvent fail,case result=null.param={}", reqParam);
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }
            log.info("notifyEvent.reqParam={}", reqParam);
            if (voidResult.rCode() != 0) {
                log.error("notifyEvent fail.rCode={}", voidResult.rCode());
                baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
                return baseResp;
            }
        } catch (Exception e) {
            log.error("handler notifyEvent error reqParam={}", reqParam, e);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        baseResp.setRetCode(RetCodeEnum.SUCCESS);
        return baseResp;
    }

    public Object sendUserHeart(GameUserHeartBeatReq reqParam) {
        BaseResp<Void> baseResp = new BaseResp<>();
        GameReportServiceProto.RoomGameUserHeartBeat.Builder builder = GameReportServiceProto.RoomGameUserHeartBeat.newBuilder()
                .setAppId(reqParam.getAppId())
                .setNjId(reqParam.getNjId())
                .setGameId(reqParam.getGameId())
                .setUserId(reqParam.getUserId())
                .setChangedTime(reqParam.getChangedTime());
        Result<Void> result = gameReportService.sendUserHeart(builder.build());
        if (result.rCode() != 0) {
            log.warn("sendUserHeart error req={}", reqParam);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
        }
        baseResp.setRetCode(RetCodeEnum.SUCCESS);
        return baseResp;
    }
}
