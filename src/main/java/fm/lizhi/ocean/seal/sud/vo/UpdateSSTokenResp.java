package fm.lizhi.ocean.seal.sud.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新长期令牌响应
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UpdateSSTokenResp {

    /**
     * 重新生成的长期令牌SSToken
     */
    private String ss_token;

    /**
     * 长期令牌SSToken的过期时间（毫秒时间戳）
     */
    private long expire_date;

}


