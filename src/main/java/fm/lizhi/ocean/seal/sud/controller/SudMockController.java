package fm.lizhi.ocean.seal.sud.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.seal.common.pojo.bo.UserInfo;
import fm.lizhi.ocean.seal.constant.GameCallbackType;
import fm.lizhi.ocean.seal.protocol.GameAppServiceProto;
import fm.lizhi.ocean.seal.service.GameAppConfigService;
import fm.lizhi.ocean.seal.sud.service.SudCallBackService;
import fm.lizhi.ocean.seal.sud.vo.GameUserHeartBeatReq;
import fm.lizhi.ocean.seal.sud.vo.ReportGameInfoReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * mock回调，上线后请把我删掉 FIXME
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("sud/mock")
@Deprecated
public class SudMockController {

    @Resource
    private GameAppConfigService gameAppConfigService;
    @Resource
    private SudCallBackService sudCallBackService;

    @RequestMapping("/mock")
    @Deprecated
    public Object mock(HttpServletRequest request) throws Exception {
        log.info("mock请求方法：{}", request.getMethod());
        log.info("mock请求uri：{}", request.getRequestURI());

        Enumeration<String> headerNames = request.getHeaderNames();
        Map<String, String> headMap = new HashMap<>();
        while (headerNames.hasMoreElements()) {
            String headName = headerNames.nextElement();
            headMap.put(headName, request.getHeader(headName));
        }
        log.info("mock请求headers：{}", JSON.toJSONString(headMap));

        byte[] byteArray = IOUtils.toByteArray(request.getInputStream());
        log.info("mock请求body：{}", new String(byteArray));

        return "success";
    }


    @RequestMapping("/mockGetUserInfo")
    @Deprecated
    public Object mockGetUserInfo(HttpServletRequest request,
                                  @RequestParam("userId") String userId) throws Exception {
        log.info("mock请求获取用户信息");
        String appId = request.getHeader("Seal-Auth-AppId");

        // 验签
        Optional<GameAppServiceProto.AppCallback> appCallbackOptional = gameAppConfigService.getGameAppCallbackConfig(appId, GameCallbackType.GET_USER);
        if (!appCallbackOptional.isPresent()) {
            throw new RuntimeException("验签失败，配置不存在");
        }
        GameAppServiceProto.AppCallback appCallback = appCallbackOptional.get();

        String key = appCallback.getCallbackKey();
        String timestamp = request.getHeader("Seal-Auth-Timestamp");
        String sign = request.getHeader("Seal-Auth-Sign");

        String orgLocalSign = String.format("%s\n%s\n%s\n", key, appId, timestamp);
        String localSign = DigestUtils.md5Hex(orgLocalSign);

        if (!Objects.equals(localSign, sign)) {
            throw new RuntimeException("验签失败");
        }

        JSONObject result = new JSONObject();
        result.put("code", 0);

        UserInfo userInfo = new UserInfo();
        userInfo.setId(Long.valueOf(userId));
        userInfo.setName("我是mock用户名称");
        userInfo.setGender(0);
        userInfo.setPortrait("https://cdnimg101.gzlzfm.com/web_res/adpush/2025/03/12/3132176364895739904.png");

        result.put("data", userInfo);

        return result;

    }

    /**
     * 模拟业务方发送用户心跳
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/sendUserHeart")
    public Object sendUserHeart(@RequestBody GameUserHeartBeatReq reqParam) {
        log.info("send user heart, appId:{}, params:{}",  reqParam);
        return sudCallBackService.sendUserHeart(reqParam);
    }


}
