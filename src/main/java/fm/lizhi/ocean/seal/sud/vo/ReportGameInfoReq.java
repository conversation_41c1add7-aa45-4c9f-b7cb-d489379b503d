package fm.lizhi.ocean.seal.sud.vo;

import fm.lizhi.ocean.seal.sud.bo.GameResult;
import lombok.*;

import java.util.Objects;

/**
 * 游戏厂商上报游戏数据
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class ReportGameInfoReq {

    /**
     * 上报类型(game_start, game_settle)
     */
    private String report_type;
    /**
     * 上报数据对象
     */
    private GameResult report_msg;
    /**
     * 接入方用户id （鉴权使用）
     */
    private String uid;
    /**
     * 接入方服务端生成ss_token （鉴权使用）
     */
    private String ss_token;

    /**
     * 游戏开始
     * @return
     */
    public boolean checkGameStart(){
        return Objects.equals(report_type, "game_start");
    }

    /**
     * 游戏结束
     * @return
     */
    public boolean checkGameSettle(){
        return Objects.equals(report_type, "game_settle");
    }
}


