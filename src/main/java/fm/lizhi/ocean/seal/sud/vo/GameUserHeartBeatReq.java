package fm.lizhi.ocean.seal.sud.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户心跳
 * <AUTHOR>
 * @date 2025/4/21 下午4:04
 * @description
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class GameUserHeartBeatReq {

    /**
     * 房主id
     */
    private Long njId;
    /**
     * 平台分配给业务方的游戏id
     */
    private Long gameId;
    /**
     * 平台分配给业务方的appId
     */
    private String appId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 变更时间戳(毫秒)
     */
    private Long changedTime;
}
