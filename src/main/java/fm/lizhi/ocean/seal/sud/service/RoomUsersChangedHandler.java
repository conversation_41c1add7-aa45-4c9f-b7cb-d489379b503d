package fm.lizhi.ocean.seal.sud.service;

import com.google.gson.JsonObject;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.sud.bo.RoomUsersChangedModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 房间用户人数变更通知处理类
 * <AUTHOR>
 * @date 2025/4/17 下午2:28
 * @description
 */
@Slf4j
@Service
public class RoomUsersChangedHandler implements SudNotifyEventHandler {

    @Resource
    private GameReportService gameReportService;

    @Override
    public Result<Void> handleNotifyEvent(JsonObject reqJsonObject, String appId) {
        JsonObject data = reqJsonObject.getAsJsonObject("data");
        RoomUsersChangedModel usersChangedModel = RoomUsersChangedModel.builder()
                .room_id(data.get("room_id").getAsString())
                .mg_id(data.get("mg_id").getAsString())
                .player_total(data.get("player_total").getAsInt())
                .ob_total(data.get("ob_total").getAsInt())
                .changed_time(data.get("changed_time").getAsString())
                .rawResult(data.toString()).build();
        return gameReportService.roomUsersChanged(usersChangedModel.buildResult(appId, GameChannel.SUD));
    }
}
