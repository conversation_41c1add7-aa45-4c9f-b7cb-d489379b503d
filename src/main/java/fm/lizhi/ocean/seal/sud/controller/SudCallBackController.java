package fm.lizhi.ocean.seal.sud.controller;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.sud.service.SudCallBackService;
import fm.lizhi.ocean.seal.sud.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * sud call back Controller
 * <p>
 * <p>
 * 这个类下面有两套方法。把appId作为url路由的方式是新的方式。旧的方式还有aiyoo在用。不能干掉
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("sud")
public class SudCallBackController {

    @Resource
    private SudCallBackService sudCallBackService;

    @PostMapping("/{appId}/get_sstoken")
    public Object getSSToken(@PathVariable("appId") String appId, @RequestBody() GetSSTokenReq reqParam) {
        log.info("请求参数：{}", JSON.toJSONString(reqParam));
        return sudCallBackService.getSSToken(reqParam.getCode(), appId);
    }

    /**
     * 刷新长期令牌
     * 调用方：游戏服务
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/{appId}/update_sstoken")
    public Object updateSSToken(@PathVariable("appId") String appId, @RequestBody() UpdateSSTokenReq reqParam) {
        return sudCallBackService.updateSSToken(reqParam.getSs_token(), appId);
    }

    /**
     * @param reqParam
     * @return
     * @deprecated get_sstoken接口返回了用户信息，这个接口应该不会被调用
     * <p>
     * 获取用户信息
     * 调用方：游戏服务
     */
    @PostMapping("/{appId}/get_user_info")
    @Deprecated
    public Object getUserInfo(@PathVariable("appId") String appId, @RequestBody() GetUserInfoReq reqParam) {
        log.warn("不应该调用get_user_info接口，{}", JSON.toJSONString(reqParam));
//        return sudCallBackService.getUserInfo(reqParam.getSs_token(), appId);
        return sudCallBackService.getUserInfoV2(reqParam.getSs_token(), appId);
    }

    /**
     * 报告游戏信息
     * 调用方：游戏服务
     *
     * @param appId    业务的appId
     * @param reqParam
     * @return
     */
    @PostMapping("/{appId}/report_game_info")
    public Object reportGameInfo(@PathVariable("appId") String appId, @RequestBody ReportGameInfoReq reqParam) {
        log.info("report game info sud, appId:{}, params:{}", appId, reqParam);
        return sudCallBackService.reportGameInfo(reqParam, appId);
        // 改造成转发给业务侧
        // return sudCallBackService.reportGameInfoHttpForward(reqParam, appId);
    }

    /**
     * 房间用户人数变更通知
     * 调用方：游戏服务
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/notifyEvent")
    public Object notifyEvent(@RequestBody String reqParam) {
        log.info("notify event sud params:{}",reqParam);
//        sudCallBackService.notifyEvent(reqParam);
        BaseResp<Void> baseResp = new BaseResp<>();
        baseResp.setRetCode(RetCodeEnum.SUCCESS);
        return baseResp;
    }

}
