package fm.lizhi.ocean.seal.sud.bo;

import com.alibaba.fastjson.JSON;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * Created in 2022-01-25 10:54.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
@Slf4j
public class GameResult {
    /**
     * 游戏id
     */
    private long mg_id;
    /**
     * 游戏id
     */
    private String mg_id_str;
    /**
     * 接入方房间id
     */
    private String room_id;
    /**
     * 游戏模式
     */
    private int game_mode;
    /**
     * 本局游戏的id （重复上报，使用该字段去重）
     */
    private String game_round_id;
    /**
     * 战斗开始时间（秒）
     */
    private int battle_start_at;
    /**
     * 战斗结束时间（秒）
     */
    private int battle_end_at;
    /**
     * 战斗总时间（秒）
     */
    private int battle_duration;
    /**
     * 游戏对象数组
     */
    private GamePlayer[] players;
    /**
     * 游戏对象数组
     */
    private GamePlayerResult[] results;
    /**
     * 游戏上报信息扩展参数（透传），取值范围：长度不超过1024字节，超过则截断
     */
    private String report_game_info_extras;

    /**
     * 转换参数
     *
     * @return
     */
    public GameReportServiceProto.GameStartResult buildGameStartResult(String appId, String channel) {
        // 额外参数
        GameExtraData gameExtraData = GameExtraData.buildGameExtraData(report_game_info_extras);
        //  游戏数据信息
        GameReportServiceProto.GameStartResult.Builder builder = GameReportServiceProto.GameStartResult.newBuilder()
                .setAppId(gameExtraData.getAppId())
                .setGameId(Long.parseLong(mg_id_str))
                .setChannelGameId(mg_id_str)
                .setGameMode(game_mode)
                .setGameRoundId(game_round_id)
                .setGameStartAtTime(battle_start_at == 0L ? System.currentTimeMillis() : battle_start_at * 1000L)
                .setReportGameInfoExtras(gameExtraData.getExtras()).setRoomId(room_id)
                .setEnv(gameExtraData.getEnv())
                .setChannel(channel)
                .setRawResult(JSON.toJSONString(this));
        if (players != null) {
            for (GamePlayer gamePlayer : players) {
                builder.addGamePlayers(gamePlayer.buildGamePlayerResult());
            }
        }
        builder.setAppId(appId);
        return builder.build();
    }

    /**
     * 转换参数
     *
     * @return
     */
    public GameReportServiceProto.GameSettleResult buildGameSettleResult(String appId, String channel) {
        // 额外参数
        GameExtraData gameExtraData = GameExtraData.buildGameExtraData(report_game_info_extras);
        // 游戏数据信息
        GameReportServiceProto.GameSettleResult.Builder builder = GameReportServiceProto.GameSettleResult.newBuilder()
                .setGameId(Long.parseLong(mg_id_str))
                .setChannelGameId(mg_id_str)
                .setGameMode(game_mode)
                .setGameRoundId(game_round_id)
                .setAppId(gameExtraData.getAppId())
                .setEnv(gameExtraData.getEnv())
                .setReportGameInfoExtras(gameExtraData.getExtras())
                .setGameStartAtTime(battle_start_at * 1000L)
                .setGameEndAtTime(battle_end_at * 1000L)
                .setGameDuration(battle_duration)
                .setRoomId(room_id)
                .setChannel(channel)
                .setRawResult(JSON.toJSONString(this));
        if (results != null) {
            for (GamePlayerResult gamePlayer : results) {
                builder.addGamePlayers(gamePlayer.buildGamePlayerSettleResult());
            }
        }
        builder.setAppId(appId);
        return builder.build();
    }

}
