package fm.lizhi.ocean.seal.sud.bo;

import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * 通知数据
 * <AUTHOR>
 * @date 2025/4/16 下午4:21
 * @description
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
@Slf4j
public class RoomUsersChangedModel {

    /**
     * 房间id
     */
    private String room_id;
    /**
     * 游戏id
     */
    private String mg_id;
    /**
     * 玩家总人数
     */
    private int player_total;
    /**
     * 观众总人数
     */
    private int ob_total;
    /**
     * 变更时间戳(毫秒)
     */
    private String changed_time;

    /**
     * 渠道原始消息
     */
    private String rawResult;

    public GameReportServiceProto.RoomUsersChangedResult buildResult(String appId, String sud) {
        GameReportServiceProto.RoomUsersChangedResult.Builder builder = GameReportServiceProto.RoomUsersChangedResult.newBuilder()
                .setAppId(appId)
                .setChannel(sud)
                .setRoomId(room_id)
                .setMgId(mg_id)
                .setPlayerTotal(player_total)
                .setObTotal(ob_total)
                .setChangedTime(changed_time)
                .setRawResult(rawResult);
        return builder.build();
    }
}
