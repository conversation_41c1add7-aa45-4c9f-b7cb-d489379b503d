package fm.lizhi.ocean.seal.sud.bo;

import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.*;

/**
 * Created in 2022-01-25 10:54.
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class GamePlayer {

    /**
     * 接入方uid，机器人为空字符
     */
    private String uid;
    /**
     * 0:普通用户，1:机器人
     */
    private int is_ai;

    /**
     * 数据转换
     *
     * @return
     */
    public GameReportServiceProto.GamePlayerResult buildGamePlayerResult() {
        return GameReportServiceProto.GamePlayerResult.newBuilder()
                .setUid(uid).setRealUser(is_ai == 0).build();
    }
}
