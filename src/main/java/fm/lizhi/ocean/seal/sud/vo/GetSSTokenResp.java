package fm.lizhi.ocean.seal.sud.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO https://docs.sud.tech/zh-CN/app/Server/HttpsCallback/get_sstoken.html 少了用户信息参数？
 *
 * 获取长期令牌响应
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GetSSTokenResp {
    /**
     * 长期令牌SSToken
     */
    private String ss_token;
    /**
     * 长期令牌SSToken的过期时间（毫秒时间戳）
     */
    private long expire_date;


     // 20250227 新增用户相关的参数
     // userInfoObj对象，当userInfoObj对象有数据时，小游戏服务端会采用该数据，不会调用getUserinfo接口，节省一次调用
     private GetUserInfoResp user_info;

}


