package fm.lizhi.ocean.seal.sud.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取用户信息响应
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class GetUserInfoResp {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 昵称
     */
    private String nick_name;

    /**
     * 头像地址（最优解为128*128）
     */
    private String avatar_url;

    /**
     * 性别（男：male, 女：female）
     */
    private String gender;

}


