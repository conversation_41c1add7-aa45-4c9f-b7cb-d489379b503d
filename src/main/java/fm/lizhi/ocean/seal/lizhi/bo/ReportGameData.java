package fm.lizhi.ocean.seal.lizhi.bo;

import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class ReportGameData {
    /**
     * 游戏id
     */
    private long gameId;

    /**
     * 游戏局id
     */
    private long gameRoundId;

    /**
     * 游戏状态
     */
    private int status;
    /**
     * 游戏创建者Id
     */
    private long hostId;
    /**
     * 开始时间
     */
    private long beginTime;
    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 游戏房间id
     */
    private String roomId;

    /**
     * 游戏参与者(玩家)
     */
    List<GameUser> gameUsers;

    /**
     * 业务额外参数
     */
    public String businessGameInfoExtras;

    /**
     * 结束游戏的原因
     */
    private int endGameType = 0;

    /**
     * 转换参数
     *
     * @return
     */
    public GameReportServiceProto.GameStartResult buildGameStartResult(String appId, String channel) {
        // 额外参数
        GameExtraData gameExtraData = GameExtraData.buildGameExtraData(businessGameInfoExtras);
        //  游戏数据信息
        GameReportServiceProto.GameStartResult.Builder builder = GameReportServiceProto.GameStartResult.newBuilder()
                .setGameId(gameId).setChannelGameId(String.valueOf(gameId)).setGameRoundId(gameRoundId+"")
                .setGameStartAtTime(beginTime == 0L ? System.currentTimeMillis() : beginTime)
                .setReportGameInfoExtras(gameExtraData.getExtras()).setRoomId(roomId).setEnv(gameExtraData.getEnv())
                .setChannel(channel);
        if (gameUsers != null) {
            for (GameUser gamePlayer : gameUsers) {
                builder.addGamePlayers(gamePlayer.buildGamePlayerResult());
            }
        }
        builder.setAppId(appId);
        return builder.build();
    }

    /**
     * 转换参数
     *
     * @return
     */
    public GameReportServiceProto.GameSettleResult buildGameSettleResult(String appId, String channel) {
        // 额外参数
        GameExtraData gameExtraData = GameExtraData.buildGameExtraData(businessGameInfoExtras);
        // 游戏数据信息
        GameReportServiceProto.GameSettleResult.Builder builder = GameReportServiceProto.GameSettleResult.newBuilder()
                .setGameId(gameId).setChannelGameId(String.valueOf(gameId)).setGameRoundId(gameRoundId+"")
                .setAppId(gameExtraData.getAppId()).setEnv(gameExtraData.getEnv()).setReportGameInfoExtras(gameExtraData.getExtras())
                .setGameStartAtTime(beginTime)
                .setGameEndAtTime(endTime).setGameDuration((int)(endTime - beginTime) / 1000).setRoomId(roomId).setChannel(channel);
        if (gameUsers != null) {
            for (GameUser gamePlayer : gameUsers) {
                builder.addGamePlayers(gamePlayer.buildGamePlayerSettleResult());
            }
        }
        builder.setAppId(appId);
        return builder.build();
    }

}

