package fm.lizhi.ocean.seal.lizhi.controller;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.game.auth.Md5TokenUtil;
import fm.lizhi.ocean.seal.RetCodeEnum;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.conf.LzConfig;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.SealRCode;
import fm.lizhi.ocean.seal.lizhi.bo.ReportGameData;
import fm.lizhi.ocean.seal.lizhi.vo.BaseResp;
import fm.lizhi.ocean.seal.lizhi.vo.ReportGameInfoReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * game call back Controller
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("lizhi-game")
public class LiZhiGameCallBackController {

    @Resource
    private GameReportService gameReportService;
    @Resource
    private LzConfig lzConfig;

    /**
     * 报告游戏信息
     * 调用方：游戏服务
     *
     * @param appId     业务的appId
     * @param reqParam
     * @return
     */
    @PostMapping("/{appId}/report-game-info")
    public Object reportGameInfo(@PathVariable("appId") String appId, @RequestBody() ReportGameInfoReq reqParam) {
        log.info("report game info, appId:{}, params:{}", appId, reqParam);
        BaseResp<Void> baseResp = new BaseResp<>();
        String uid = reqParam.getUid();
        String ssToken = reqParam.getSsToken();
        //此处应该加入secret。另外通过appId来进行获取，而不是配置获取。此处先不处理
        String token = Md5TokenUtil.genMd5Token(uid + lzConfig.getSeal2PongAppId());
        if (!Objects.equals(ssToken, token)) {
            log.warn("gameReport fail,case no auth.param={}", reqParam.toString());
            baseResp.setSdkErrorCode(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR);
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        ReportGameData gameResult = reqParam.getReportData();
        Result<Void> result = null;
        if (reqParam.checkGameStart()) {
            result = gameReportService.gameStartReport(gameResult.buildGameStartResult(appId, GameChannel.LIZHI));
        } else if (reqParam.checkGameSettle()) {
            result = gameReportService.gameSettleReport(gameResult.buildGameSettleResult(appId, GameChannel.LIZHI));
        }
        if (result == null) {
            log.error("gameReport fail,case result=null.param={}", reqParam.toString());
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        if (result.rCode() != 0) {
            log.error("gameReport fail.rCode={}", result.rCode());
            baseResp.setRetCode(RetCodeEnum.REQUEST_FAILED);
            return baseResp;
        }
        baseResp.setRetCode(RetCodeEnum.SUCCESS);
        return baseResp;
    }

}
