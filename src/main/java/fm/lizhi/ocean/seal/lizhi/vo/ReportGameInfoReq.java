package fm.lizhi.ocean.seal.lizhi.vo;

import fm.lizhi.ocean.seal.lizhi.bo.ReportGameData;
import lombok.*;

import java.util.Objects;

/**
 * 游戏厂商上报游戏数据
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@ToString
public class ReportGameInfoReq {

    /**
     * 上报类型(1开始, 2结束)
     */
    private int reportType;
    /**
     * 上报数据对象
     */
    private ReportGameData reportData;
    /**
     * 接入方用户id （鉴权使用）
     */
    private String uid;
    /**
     * 接入方服务端生成ssToken （鉴权使用）
     */
    private String ssToken;

    /**
     * 游戏开始
     *
     * @return
     */
    public boolean checkGameStart() {
        return Objects.equals(reportType, 1);
    }

    /**
     * 游戏结束
     *
     * @return
     */
    public boolean checkGameSettle() {
        return Objects.equals(reportType, 2);
    }
}


