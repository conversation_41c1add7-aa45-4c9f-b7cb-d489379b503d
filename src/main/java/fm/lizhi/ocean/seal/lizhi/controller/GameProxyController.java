//package fm.lizhi.ocean.seal.lizhi.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import fm.lizhi.ocean.seal.lizhi.bo.AppIdAppKey;
//import fm.lizhi.ocean.seal.lizhi.vo.ProxyResult;
//import fm.lizhi.ocean.seal.service.GameProxyService;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//
///**
// * Created in 2022-06-23 15:55.
// *
// * <AUTHOR>
// */
//@RestController
//@RequestMapping("/game/proxy/")
//public class GameProxyController {
//    private static final Logger logger = LoggerFactory.getLogger(GameProxyController.class);
//    @Resource(name = "gameProxyServiceImpl")
//    private GameProxyService gameProxyService;
//
//    /**
//     * 调用目标方法
//     * curl -d '{"appId": 30129761, "gameId": "xx", "type": 1, "interface": "/game/proxy/test/invoke", "params": "{}"}' -X POST -H 'sign:0ddc2313ac9c00a109b4a9ea610cb562' -H 'Content-Type: application/json' 'http://commonseal.yfxn.lizhi.fm/game/proxy/invokeTarget'
//     * 游戏调用业务, type = 1
//     * 业务调用游戏, type = 2
//     * @return
//     */
//    @PostMapping("/invokeTarget")
//    public ProxyResult invokeTarget(HttpServletRequest request, @RequestBody JSONObject params) {
//        ProxyResult response = new ProxyResult();
//        String appId = params.getString("appId");
//        String gameId = params.getString("gameId");
//        int type = params.getIntValue("type");
//        String interfaceAddr = params.getString("interface");
//        boolean validType = type == 1 || type == 2;
//        if (StringUtils.isBlank(appId) || StringUtils.isBlank(gameId) || StringUtils.isBlank(interfaceAddr) || !validType) {
//            logger.info("Failed to invoke target interface, parameter error, appId:{}, gameId:{}, type:{}, interface:{}", appId, gameId, type, interfaceAddr);
//            response.setCode(1);
//            response.setMsg("parameter error");
//            return response;
//        }
//
//        // 签名校验
//        String sign = request.getHeader("sign");
//        // 根据业务APPID获取渠道的appId与appKey。此接口存在业务调用、第三方调用。但不管业务调用、还是第三方调用。appId为平台颁发给业务的appId
//        //这里获取到的appIdAppKey 需要根据场景返回。如果业务调用，那么验签名。应使用业务。如果是第三方调用，验证签名使用第三方。
//        //小心这里的type。游戏调业务，传入为1。但是这里需要验签为游戏。所以要传入为2。如果type=2，那么是业务调用游戏，需要验签为业务。传入为1
//        AppIdAppKey result = this.gameProxyService.getChannelAppIdAndAppKey(
//                appId, gameId, type == NumberUtils.INTEGER_ONE ? 2 : NumberUtils.INTEGER_ONE);
//        if (result == null) {
//            logger.info("Failed to invoke target interface, param error, appId:{}, gameId:{}, type:{}, interface:{}", appId, gameId, type, interfaceAddr);
//            response.setCode(1);
//            response.setMsg("parameter error");
//            return response;
//        }
//        String serverSign = this.gameProxyService.sign(params, result);
//        if (!StringUtils.equals(sign, serverSign)) {
//            logger.info("Failed to invoke target interface, sign error, appId:{}, gameId:{}, type:{}, interface:{}", appId, gameId, type, interfaceAddr);
//            response.setCode( 2);
//            response.setMsg("sign error");
//            return response;
//        }
//
//        try {
//            return this.gameProxyService.invokeTarget(appId, gameId, type, interfaceAddr, params.getString("params"));
//        } catch (Exception e) {
//            response.setCode(3);
//            response.setMsg("service exception");
//            logger.error("Failed to invoke target interface, service exception, appId:{}, gameId:{}, type:{}, interface:{}", appId, gameId, type, interfaceAddr, e);
//            return response;
//        }
//    }
//
//    /**
//     * 测试方法
//     *
//     * @param params 参数
//     * @return
//     */
//    @PostMapping("/test/invoke")
//    public JSONObject testInvoke(@RequestBody JSONObject params) {
//        JSONObject jsonObject = new JSONObject();
//        logger.info("Call test invoke, params:{}", params);
//        jsonObject.put("code", 0);
//        jsonObject.put("msg", "");
//        jsonObject.put("data", "{\"username\": \"Horace\"}");
//        return jsonObject;
//    }
//}