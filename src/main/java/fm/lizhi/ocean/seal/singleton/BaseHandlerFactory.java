package fm.lizhi.ocean.seal.singleton;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 基础工厂
 * <AUTHOR>
 * @date 2025/4/17 下午2:41
 * @description
 */
@Slf4j
public abstract class BaseHandlerFactory<T, H> implements HandlerFactory<T, H> {
    protected final Map<T, H> HANDLERS = new HashMap<>();

    @PostConstruct
    public abstract void registerAllHandlers();

    protected void registerHandler(T type, H handler) {
        if (HANDLERS.containsKey(type)) {
            log.error("handler type has registered. type={}, existedHandler={}, currentHandler={}",
                    type, HANDLERS.get(type), handler);
            return;
        }
        HANDLERS.put(type, handler);
    }

    @Override
    public H getHandler(T type) {
        H handler = HANDLERS.get(type);
        if (handler == null) {
            log.info("handler not found. type: {}, handlerClass: {}", type, this.getClass().getSimpleName());
        }
        return handler;
    }
}
