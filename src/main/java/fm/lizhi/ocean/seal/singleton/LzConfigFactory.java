package fm.lizhi.ocean.seal.singleton;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import fm.lizhi.commons.config.service.ConfigService;
import fm.lizhi.ocean.seal.conf.LzConfig;

@Configuration
public class LzConfigFactory {

	@Bean(name = "lzLzConfig")
	public LzConfig get() throws Exception {
		LzConfig lzLzConfig = ConfigService.loadConfig(LzConfig.class);
		return lzLzConfig;
	}

}
