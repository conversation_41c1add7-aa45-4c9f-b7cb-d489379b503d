package fm.lizhi.ocean.seal.singleton;

import fm.lizhi.commons.service.client.dubbo.generic.GenericService;
import fm.lizhi.commons.service.client.proxy.ProxyBuilder;
import fm.lizhi.commons.util.GuidGenerator;
import fm.lizhi.ocean.seal.api.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 服务消费者service初始化类
 */
@Configuration
public class ServiceFactory {

    @Resource
    private ProxyBuilder proxyBuilder;

//    @Bean(name = "pongUserService")
//    public CustomerService pongUserService() {
//        return proxyBuilder.buildProxy(CustomerService.class);
//    }
//
//    @Bean(name = "ppNewUserService")
//    public PpNewUserService ppNewUserService() {
//        return proxyBuilder.buildProxy(PpNewUserService.class);
//    }

    @Bean(name = "gameAuthService")
    public GameAuthService sudAuthService() {
        return proxyBuilder.buildProxy(GameAuthService.class);
    }

    @Bean(name = "gameReportService")
    public GameReportService gameReportService() {
        return proxyBuilder.buildProxy(GameReportService.class);
    }

    @Bean(name = "sealTokenService")
    public SealTokenService sealTokenService() {
        return proxyBuilder.buildProxy(SealTokenService.class);
    }

    @Bean
    public GenericService genericService() {
        return proxyBuilder.buildProxy(GenericService.class);
    }

    @Bean
    public GuidGenerator guidGenerator() {
        return new GuidGenerator();
    }

    @Bean
    public GameAppService appConfigService() {
        return proxyBuilder.buildProxy(GameAppService.class);
    }

    @Bean
    public GameInfoService gameInfoService() {
        return proxyBuilder.buildProxy(GameInfoService.class);
    }

    @Bean
    public GameService gameService() {
        return proxyBuilder.buildProxy(GameService.class);
    }

    @Bean
    public GameRoundService gameRoundService() {
        return proxyBuilder.buildProxy(GameRoundService.class);
    }

    @Bean
    public GameProxyService gameProxyService() {
        return proxyBuilder.buildProxy(GameProxyService.class);
    }
}
