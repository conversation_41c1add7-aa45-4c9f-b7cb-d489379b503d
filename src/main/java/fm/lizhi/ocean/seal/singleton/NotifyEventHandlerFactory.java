package fm.lizhi.ocean.seal.singleton;

import fm.lizhi.ocean.seal.constant.NotifyEventEnum;
import fm.lizhi.ocean.seal.sud.service.RoomUsersChangedHandler;
import fm.lizhi.ocean.seal.sud.service.SudNotifyEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/4/17 下午2:52
 * @description
 */
@Slf4j
@Service
public class NotifyEventHandlerFactory extends  BaseHandlerFactory<String, SudNotifyEventHandler> {

    @Resource
    private RoomUsersChangedHandler roomUsersChangedHandler;

    @Override
    @PostConstruct
    public void registerAllHandlers() {
        registerHandler(NotifyEventEnum.ROOM_USERS_CHANGED.getCode(), roomUsersChangedHandler);
    }
}
