package fm.lizhi.ocean.seal.conf;

import io.netty.util.concurrent.DefaultThreadFactory;
import org.apache.coyote.ProtocolHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created in 2022-04-29 15:22.
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfig implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {
    private static final Logger logger = LoggerFactory.getLogger(WebConfig.class);

    // FIXME 注释了webSocket的逻辑
//    @Bean
//    public ServerEndpointExporter serverEndpointExporter() {
//        return new ServerEndpointExporter();
//    }

    @Override
    public void customize(TomcatServletWebServerFactory factory) {
        factory.addConnectorCustomizers(connector -> {
            ProtocolHandler protocolHandler = connector.getProtocolHandler();
            ThreadPoolExecutor executor = this.createThreadPoolExecutor();
            protocolHandler.setExecutor(executor);
        });
    }

    /**
     * 创建线程池
     *
     * @return
     */
    private ThreadPoolExecutor createThreadPoolExecutor() {
        return new ThreadPoolExecutor(10, 500, 3,
                TimeUnit.MINUTES, new LinkedBlockingQueue<>(500), new DefaultThreadFactory("http-work"));
    }
}