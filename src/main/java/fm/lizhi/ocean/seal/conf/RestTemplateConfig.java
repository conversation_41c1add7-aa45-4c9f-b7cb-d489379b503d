package fm.lizhi.ocean.seal.conf;

import com.google.common.base.Splitter;
import fm.lizhi.biz.data.collector.DataCtxContainer;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * Http请求模板配置
 * <p>
 * Created in 2022-04-18 12:35.
 *
 * <AUTHOR>
 */
@Configuration
public class RestTemplateConfig {
    private static final Logger logger = LoggerFactory.getLogger(RestTemplateConfig.class);

    /**
     * Http请求模板
     *
     * @return
     */
    @Bean
    public RestTemplate restTemplate() {
        // 连接管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(500);
        connectionManager.setDefaultMaxPerRoute(500);
        connectionManager.setValidateAfterInactivity((int) TimeUnit.SECONDS.toMillis(3));

        // 请求工厂
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectionRequestTimeout((int) TimeUnit.SECONDS.toMillis(3));
        factory.setConnectTimeout((int) TimeUnit.SECONDS.toMillis(3));
        factory.setReadTimeout((int) TimeUnit.SECONDS.toMillis(5));
        CloseableHttpClient client = HttpClients.custom()
                .setConnectionTimeToLive(5, TimeUnit.MINUTES)
                .setConnectionManager(connectionManager)
                .build();

        factory.setHttpClient(client);
        RestTemplate restTemplate = new RestTemplate(factory);
        restTemplate.setInterceptors(Collections.singletonList((request, body, execution) -> {
            ClientHttpResponse response = null;
            long startTime = System.currentTimeMillis();
            long endTime;
            try {
                request.getHeaders().set("X-Real-IP", getUserIpFromContext());
                response = execution.execute(request, body);
                endTime = System.currentTimeMillis();
            } catch (Exception e) {
                endTime = System.currentTimeMillis();
                logger.error("failed to request interface, time:{}ms, uri:{}, requestBody:{}, msg:{}", (endTime - startTime), request.getURI(), new String(body), e.getMessage(), e);
                throw e;
            }
            logger.info("request interface, time:{}ms, uri:{}, requestBody:{}", (endTime - startTime), request.getURI(), new String(body));
            return response;
        }));
        return restTemplate;
    }

    /**
     * 解决灯塔路由问题，传递IP到下游
     *
     * @return
     */
    public static String getUserIpFromContext() {
        String userIp = null;
        if (DataCtxContainer.get() != null && DataCtxContainer.get().getAppHeadCtx() != null) {
            userIp = DataCtxContainer.get().getAppHeadCtx().getIp();
            logger.info("get userIp from AppHeadCtx!userIp:[{}]", userIp);
        } else if (DataCtxContainer.get() != null && DataCtxContainer.get().getHttpCtx() != null) {
            userIp = DataCtxContainer.get().getHttpCtx().getIp();
            logger.info("get userIp from HttpCtx!userIp:[{}]", userIp);
        }
        if (StringUtils.isNotEmpty(userIp) && userIp.contains(":")) {
            userIp = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(userIp).get(0);
        }
        return userIp;
    }
}