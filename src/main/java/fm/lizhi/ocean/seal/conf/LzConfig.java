package fm.lizhi.ocean.seal.conf;

import lombok.Data;

@Data
public class LzConfig {

    private String dcProxyConfigKey;
    private int dcProxyConnectionCount;

    /**
     * mongo
     */
    private String mongoHosts;

    /**
     * mongodb数据库名
     */
    private String mongoDatabase = "ocean_seal";

    private int connMongodbConnPerHost = 100;

    /**
     * 荔枝自研游戏颁发给Pong的游戏AppId
     */
    private String seal2PongAppId;
    private int webSocketPort = 1577;
    /**
     * WebSocket启用Token校验
     */
    private boolean webSocketTokenVerify = false;
    /**
     * websocket连接清理周期，单位秒
     */
    private int webSocketCleanPeriod = 3;
    /**
     * websocket连接过期时间，单位秒
     */
    private int webSocketConnectExpire = 30;
    /**
     * websocket调试日志
     */
    private boolean webSocketDebugLog = false;
    /**
     * 游戏状态过期时间，默认60秒
     */
    private int gameStateExpireTime = 10 * 60;

    /**
     * sud回调是否验签
     */
    private boolean sudCheckCallbackSign = true;

    /**
     * luck回调是否验签
     */
    private boolean luckCheckCallbackSign = true;

}
