package fm.lizhi.ocean.seal.conf;

import javax.servlet.Filter;
import javax.servlet.http.HttpServletRequest;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import fm.lizhi.biz.data.collector.DataCollectFilter;
import fm.lizhi.biz.data.collector.Token2UserTranslator;

@Configuration
public class DataCollectConfig {

    @Bean
    public FilterRegistrationBean<Filter> dataCollectFilter() {
        FilterRegistrationBean<Filter> registration = new FilterRegistrationBean<>();
        DataCollectFilter filter = new DataCollectFilter(new Token2UserTranslator() {
            @Override
            public long translateFromTokenToUserId(HttpServletRequest request) {
                return 0L;
            }
        });
        registration.setFilter(filter);
        registration.addUrlPatterns("/*");
        registration.setName("dataCollect-filter");
        registration.setOrder(0);
        return registration;
    }
}
