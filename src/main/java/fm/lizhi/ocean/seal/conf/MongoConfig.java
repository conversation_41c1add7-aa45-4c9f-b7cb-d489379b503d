// FIXME 2025-02-27 精简暂时用不到的代码

//package fm.lizhi.ocean.seal.conf;
//
//
//import com.google.common.base.Function;
//import com.google.common.base.Splitter;
//import com.google.common.collect.FluentIterable;
//import com.mongodb.*;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.data.mongodb.MongoDbFactory;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.SimpleMongoDbFactory;
//
//import java.util.List;
//
//
///**
// * <AUTHOR> dandan
// * @className MongoConfig
// * @description mongo连接配置
// * @date 2020/8/27 15:57
// **/
//@Slf4j
//@Configuration
//public class MongoConfig {
//
//    @Autowired
//    LzConfig lzConf;
//
//    public final static String SPLIT_COMMA = ",";
//
//    public final static String SPLIT_COLON = ":";
//
//    @Primary
//    @Bean("mongoClient")
//    public com.mongodb.MongoClient getMongoClient() {
//        MongoClientOptions options = MongoClientOptions.builder()
//                .connectionsPerHost(lzConf.getConnMongodbConnPerHost())
//                .cursorFinalizerEnabled(false)
//                .socketTimeout(60000)
//                .connectTimeout(15000)
//                .maxConnectionIdleTime(600000)
//                .build();
//        List<ServerAddress> servers = FluentIterable.from(Splitter.on(SPLIT_COMMA).splitToList(lzConf.getMongoHosts()))
//                .transform(new Function<String, ServerAddress>() {
//                    @Override
//                    public ServerAddress apply(String hostAndPortStr) {
//                        List<String> hostAndPort = Splitter.on(SPLIT_COLON).splitToList(hostAndPortStr);
//                        return new ServerAddress(hostAndPort.get(NumberUtils.INTEGER_ZERO), Integer.valueOf(hostAndPort.get(NumberUtils.INTEGER_ONE)));
//                    }
//                }).toList();
//
//        com.mongodb.MongoClient mongoClient = new MongoClient(servers, options);
//        mongoClient.setWriteConcern(WriteConcern.SAFE);
//        return mongoClient;
//    }
//
//
//    @Primary
//    @Bean
//    public MongoDbFactory getMongoDbFactory() {
//        return new SimpleMongoDbFactory(getMongoClient(), lzConf.getMongoDatabase());
//    }
//
//
//    /**
//     * 注入MongoTemplate，就可以直接使用该方法进行操作
//     *
//     * @return
//     */
//    @Primary
//    @Bean(name = "mongoTemplate")
//    public MongoTemplate getMongoTemplate() {
//        MongoTemplate mongoTemplate = new MongoTemplate(getMongoDbFactory());
//        return mongoTemplate;
//    }
//
//    @Bean
//    public Mongo mongo() {
//        return getMongoClient();
//    }
//}
//
