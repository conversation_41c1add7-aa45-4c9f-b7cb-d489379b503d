package fm.lizhi.ocean.seal;

import lombok.Getter;
import lombok.Setter;

/**
 * 返回码枚举定义
 *
 * <AUTHOR>
 */
public enum RetCodeEnum {
    /**
     * 成功
     */
    SUCCESS(0, "success"),

    /**
     * 失败
     */
    REQUEST_FAILED(1, "失败");

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private int index;

    RetCodeEnum(int index, String name) {
        this.name = name;
        this.index = index;
    }


}
