package fm.lizhi.ocean.seal.agora.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import fm.lizhi.ocean.seal.agora.constants.AgoraEventType;
import fm.lizhi.ocean.seal.agora.handler.CallbackHandler;
import fm.lizhi.ocean.seal.agora.handler.HandlerManager;
import fm.lizhi.ocean.seal.agora.vo.AgoraMsgCode;
import fm.lizhi.ocean.seal.agora.vo.AgoraResponseVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 声网回调接口
 * <p>
 * Created in 2022-05-13 10:53.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/agora")
public class AgoraCallbackController {
    private static final Logger logger = LoggerFactory.getLogger(AgoraCallbackController.class);
    @Resource
    private HandlerManager handlerManager;

    /**
     * 声网游戏回调
     * curl -d '{"id": 1}' -X POST -H 'Content-Type: application/json' 'http://localhost:8080/agora/1234/game/callback'
     *
     * @param appId  业务方的appId
     * @param params 参数
     * @return
     */
    @PostMapping("/{appId}/game/callback")
    public AgoraResponseVo<?> callback(@PathVariable("appId") String appId, @RequestBody JSONObject params) {
        logger.info("Agora callback, appId:{}, params:{}", appId, params);
        String eventTypeString = params.getString("eventType");
        if (StringUtils.isEmpty(eventTypeString)) {
            logger.error("Agora callback error, eventType is empty, appId:{}, params:{}", appId, params);
            return AgoraResponseVo.msgCode(AgoraMsgCode.ERROR_PARAMS);
        }
        int eventType = Integer.parseInt(eventTypeString);
        AgoraEventType type = AgoraEventType.from(eventType);
        if (type == null) {
            logger.error("Agora callback error, event type is null, appId:{}, params:{}", appId, params);
            return AgoraResponseVo.error();
        }
        // 根据不同类型事件，解析不同类型参数对象
        String jsonParam = JSON.toJSONString(params);
        CallbackHandler handler = this.handlerManager.getHandler(type);
        if (handler == null) {
            logger.error("Agora callback error, callback handler is null, appId:{}, params:{}", appId, params);
            return AgoraResponseVo.error();
        }
        return handler.handler(appId, jsonParam);
    }
}