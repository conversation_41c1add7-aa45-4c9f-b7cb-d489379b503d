package fm.lizhi.ocean.seal.agora.handler;

import fm.lizhi.ocean.seal.agora.constants.AgoraEventType;
import fm.lizhi.ocean.seal.agora.vo.AgoraResponseVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 游戏玩家发送弹幕
 * Created in 2022-05-13 11:41.
 *
 * <AUTHOR>
 */
@Service
public class PlayerSendBraggerCallbackHandler implements CallbackHandler {
    private static final Logger logger = LoggerFactory.getLogger(PlayerSendBraggerCallbackHandler.class);

    /**
     * 能够处理的事件类型
     *
     * @return
     */
    @Override
    public AgoraEventType getAccept() {
        return AgoraEventType.PLAYER_SEND_BRAGGER;
    }

    /**
     * 处理方法
     *
     *
     * @param appId
     * @param jsonParam json类型参数
     * @return
     */
    @Override
    public AgoraResponseVo<?> handler(String appId, String jsonParam) {
        return AgoraResponseVo.success();
    }
}