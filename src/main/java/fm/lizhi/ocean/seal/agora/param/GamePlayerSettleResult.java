package fm.lizhi.ocean.seal.agora.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-13 10:36.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class GamePlayerSettleResult {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 是否是真实的用户
     */
    private boolean realUser;
    /**
     * 游戏角色
     */
    private int role;
    /**
     * 是否逃跑
     */
    private boolean escaped;
    /**
     * 排名，从1开始，平局排名相同
     */
    private int rank;
    /**
     * 得分
     */
    private int score;
    /**
     * 游戏胜负结果：0 表示没有信息，1 表示输，2 表示赢，3 表示平局
     */
    private int isWin;
}