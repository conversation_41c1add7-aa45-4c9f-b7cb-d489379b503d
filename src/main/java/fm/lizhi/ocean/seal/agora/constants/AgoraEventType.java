package fm.lizhi.ocean.seal.agora.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 声网互动游戏回调事件类型
 * <p>
 * Created in 2022-05-13 09:56.
 *
 * <AUTHOR>
 */
public enum AgoraEventType {
    //
    GAME_START(101, "游戏房间开始"),
    GAME_END(102, "游戏房间结束"),
    GAME_JOIN(110, "游戏玩家加入"),
    GAME_LEAVE(111, "游戏玩家离开"),
    PLAYER_SEND_GIFT(120, "游戏玩家送礼物"),
    PLAYER_SEND_BRAGGER(121, "游戏玩家发送弹幕"),
    ;
    private int value;
    private String msg;

    private static Map<Integer, AgoraEventType> map = new HashMap<>();

    static {
        for (AgoraEventType object : AgoraEventType.values()) {
            map.put(object.getValue(), object);
        }
    }

    AgoraEventType(int value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public int getValue() {
        return value;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据值类型找枚举
     *
     * @param value 值
     * @return
     */
    public static AgoraEventType from(int value) {
        return map.get(value);
    }
}