package fm.lizhi.ocean.seal.agora.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 公共参数
 * <p>
 * Created in 2022-05-13 10:02.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class CommonParam {
    /**
     * 事件 ID，标识来自 GameServer 服务器的一次事件通知，需要保持全局唯一，即使出现重复上报，第三方游戏平台可用于去重。
     */
    private String eventId;
    /**
     * 游戏ID
     */
    private String gameId;
    /**
     * 游戏房间ID
     */
    private String roomId;
    /**
     * 本局游戏ID(保持全局唯一)
     */
    private String gameRoundId;
    /**
     * 事件类型枚举
     */
    private int eventType;
    /**
     * 序列号，标识该事件在 GameServer上发生的顺序。
     * 注意：需要 GameServer 在 gameRoundId进行对seq全局单调递增，第三方游戏平台可对
     * 某局游戏进行事件排序
     */
    private long seq;
    /**
     * GameServer 上报事件的 UTC 时间戳，精确到13位毫秒
     */
    private long reportedMs;
    /**
     * 第三方平台ID，平台租户
     */
    private String platformId;
}