package fm.lizhi.ocean.seal.agora.handler;

import com.alibaba.fastjson.JSON;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.ocean.seal.agora.adapter.GameParamsAdapter;
import fm.lizhi.ocean.seal.agora.constants.AgoraEventType;
import fm.lizhi.ocean.seal.agora.param.GameEndParam;
import fm.lizhi.ocean.seal.agora.vo.AgoraResponseVo;
import fm.lizhi.ocean.seal.api.GameReportService;
import fm.lizhi.ocean.seal.constant.GameChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 游戏房间结束
 * Created in 2022-05-13 11:41.
 *
 * <AUTHOR>
 */
@Service
public class GameEndCallbackHandler implements CallbackHandler {
    private static final Logger logger = LoggerFactory.getLogger(GameEndCallbackHandler.class);
    @Resource
    private GameReportService gameReportService;
    @Resource
    private GameParamsAdapter gameParamsAdapter;

    /**
     * 能够处理的事件类型
     *
     * @return
     */
    @Override
    public AgoraEventType getAccept() {
        return AgoraEventType.GAME_END;
    }

    /**
     * 处理方法
     *
     * @param appId     业务方appId
     * @param jsonParam json类型参数
     * @return
     */
    @Override
    public AgoraResponseVo<?> handler(String appId, String jsonParam) {
        GameEndParam param = JSON.parseObject(jsonParam, GameEndParam.class);
        Result<Void> result = this.gameReportService.gameSettleReport(this.gameParamsAdapter.convert(appId, param, GameChannel.AGORA));
        if (result.rCode() != GeneralRCode.GENERAL_RCODE_SUCCESS) {
            logger.error("Agora callback error, game end report error, appId:{}, rCode:{}, params:{} ", appId, result.rCode(), jsonParam);
            return AgoraResponseVo.error();
        }
        return AgoraResponseVo.success();
    }
}