package fm.lizhi.ocean.seal.agora.adapter;

import fm.lizhi.ocean.seal.agora.param.GameEndParam;
import fm.lizhi.ocean.seal.agora.param.GamePlayer;
import fm.lizhi.ocean.seal.agora.param.GamePlayerSettleResult;
import fm.lizhi.ocean.seal.agora.param.GameStartParam;
import fm.lizhi.ocean.seal.protocol.GameReportServiceProto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Created in 2022-05-13 16:04.
 *
 * <AUTHOR>
 */
@Component
public class GameParamsAdapter {
    private static final Logger logger = LoggerFactory.getLogger(GameParamsAdapter.class);

    /**
     * 转换
     *
     * @param appId 业务方的appId
     * @param param 参数
     * @return
     */
    public GameReportServiceProto.GameStartResult convert(String appId, GameStartParam param, String channel) {
        GameReportServiceProto.GameStartResult.Builder builder = GameReportServiceProto.GameStartResult.newBuilder();
        //builder.setGameId(Long.parseLong(param.getGameId())); // 参考channelGameId
        builder.setRoomId(param.getRoomId());
        builder.setGameMode(param.getGameMode());
        builder.setGameRoundId(param.getGameRoundId());
        builder.setGameStartAtTime(param.getGameStartAtTime());
        //builder.setReportGameInfoExtras(param.getReportGamInfoExtras());
        builder.addAllGamePlayers(this.convertGamePlayers(param.getGamePlayers()));
        builder.setAppId(appId);
        builder.setEnv(-1);
        builder.setChannel(channel);
        builder.setChannelGameId(param.getGameId());
        return builder.build();
    }

    /**
     * 转换
     *
     * @param appId 业务方的appId
     * @param param 参数
     * @return
     */
    public GameReportServiceProto.GameSettleResult convert(String appId, GameEndParam param, String channel) {
        GameReportServiceProto.GameSettleResult.Builder builder = GameReportServiceProto.GameSettleResult.newBuilder();
        //builder.setGameId(Long.parseLong(param.getGameId())); // 参考channelGameId
        builder.setRoomId(param.getRoomId());
        builder.setGameMode(param.getGameMode());
        builder.setGameRoundId(param.getGameRoundId());
        builder.setGameStartAtTime(param.getGameStartAtTime());
        builder.setGameEndAtTime(param.getGameEndAtTime());
        builder.setGameDuration(param.getGameDuration());
        builder.addAllGamePlayers(this.convertGameSettlePlayers(param.getGamePlayerSettleResults()));
        builder.setAppId(appId);
        builder.setEnv(-1);
        builder.setReportGameInfoExtras("");
        builder.setChannel(channel);
        builder.setChannelGameId(param.getGameId());
        return builder.build();
    }

    /**
     * 转换Protobuf
     *
     * @param gamePlayerSettleResults 游戏结果数组
     * @return
     */
    private List<GameReportServiceProto.GamePlayerSettleResult> convertGameSettlePlayers(GamePlayerSettleResult[] gamePlayerSettleResults) {
        List<GameReportServiceProto.GamePlayerSettleResult> results = new ArrayList<>(gamePlayerSettleResults.length);
        for (GamePlayerSettleResult player : gamePlayerSettleResults) {
            GameReportServiceProto.GamePlayerSettleResult.Builder builder = GameReportServiceProto.GamePlayerSettleResult.newBuilder();
            builder.setUid(player.getUserId());
            builder.setRealUser(player.isRealUser());
            builder.setEscaped(player.isEscaped());
            builder.setRank(player.getRank());
            builder.setScore(player.getScore());
            builder.setRole(player.getRole());
            builder.setIsWin(player.getIsWin());
            results.add(builder.build());
        }
        return results;
    }

    /**
     * 转换游戏玩家对象
     *
     * @param gamePlayers 玩家集合
     * @return
     */
    private List<GameReportServiceProto.GamePlayerResult> convertGamePlayers(GamePlayer[] gamePlayers) {
        List<GameReportServiceProto.GamePlayerResult> results = new ArrayList<>(gamePlayers.length);
        for (GamePlayer player : gamePlayers) {
            GameReportServiceProto.GamePlayerResult.Builder builder = GameReportServiceProto.GamePlayerResult.newBuilder();
            builder.setUid(player.getUserId());
            builder.setRealUser(player.isRealUser());
            results.add(builder.build());
        }
        return results;
    }
}