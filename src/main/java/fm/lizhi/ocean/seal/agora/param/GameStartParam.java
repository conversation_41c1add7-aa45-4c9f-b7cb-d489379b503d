package fm.lizhi.ocean.seal.agora.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-13 10:10.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class GameStartParam extends CommonParam {
    /**
     * 游戏模式
     */
    private int gameMode;
    /**
     * 游戏真正开始时间(UTC 时间戳)
     */
    private long gameStartAtTime;
    /**
     * 玩家集合
     */
    private GamePlayer[] gamePlayers;
    /**
     * 游戏上报信息扩展参数，取值范围：长度不超过1024字节，超过则截断
     */
    private String reportGamInfoExtras;
}