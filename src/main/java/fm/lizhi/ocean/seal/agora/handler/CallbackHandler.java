package fm.lizhi.ocean.seal.agora.handler;

import fm.lizhi.ocean.seal.agora.constants.AgoraEventType;
import fm.lizhi.ocean.seal.agora.vo.AgoraResponseVo;

/**
 * 声网回调处理器
 * <p>
 * Created in 2022-05-13 11:29.
 *
 * <AUTHOR>
 */
public interface CallbackHandler {
    /**
     * 能够处理的事件类型
     *
     * @return
     */
    public AgoraEventType getAccept();

    /**
     * 处理方法
     *
     * @param appId     业务方的appId
     * @param jsonParam json类型参数
     * @return
     */
    public AgoraResponseVo<?> handler(String appId, String jsonParam);
}
