package fm.lizhi.ocean.seal.agora.handler;

import fm.lizhi.ocean.seal.agora.constants.AgoraEventType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 处理器管理器
 * <p>
 * Created in 2022-05-13 11:32.
 *
 * <AUTHOR>
 */
@Component
public class HandlerManager implements ApplicationContextAware {
    private static final Logger logger = LoggerFactory.getLogger(HandlerManager.class);
    private Map<AgoraEventType, CallbackHandler> handlerMap = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        String[] names = applicationContext.getBeanNamesForType(CallbackHandler.class);
        for (String name : names) {
            CallbackHandler handler = (CallbackHandler) applicationContext.getBean(name);
            handlerMap.put(handler.getAccept(), handler);
            logger.info("Find the agora callback event handler, eventType:{}, handler:{}", handler.getAccept(), handler);
        }
    }

    /**
     * 获取处理器对象
     *
     * @param eventType 事件类型
     * @return
     */
    public CallbackHandler getHandler(AgoraEventType eventType) {
        return this.handlerMap.get(eventType);
    }
}