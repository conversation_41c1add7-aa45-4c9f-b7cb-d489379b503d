package fm.lizhi.ocean.seal.agora.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created in 2022-05-13 10:33.
 *
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
public class GameEndParam extends CommonParam {
    /**
     * 游戏模式
     */
    private int gameMode;
    /**
     * 游戏真正开始时间(UTC 时间戳)
     *
     * @param 时间戳
     */
    private long gameStartAtTime;
    /**
     * 游戏真正结束时间(UTC 时间戳)
     */
    private long gameEndAtTime;
    /**
     * 服务端记录的游戏真正进行时间(精确到秒)
     */
    private int gameDuration;
    /**
     * 游戏结果数组
     */
    private GamePlayerSettleResult[] gamePlayerSettleResults;
}