package fm.lizhi.ocean.seal.agora.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import fm.lizhi.ocean.seal.websocket.vo.MsgCode;

/**
 * Created in 2022-05-13 11:28.
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties("success")
public class AgoraResponseVo<T> {
    /**
     * 服务端处理状态码
     */
    private int code;
    /**
     * 提示信息
     */
    private String msg;
    /**
     * 数据体
     */
    private T data;

    public AgoraResponseVo() {
    }

    public AgoraResponseVo(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public AgoraResponseVo(AgoraMsgCode msgCode) {
        this.code = msgCode.getCode();
        this.msg = msgCode.getMsg();
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    /**
     * 成功
     *
     * @param <T>  类型参数
     * @param data 响应数据
     * @return
     */
    public static <T> AgoraResponseVo<T> success(T data) {
        AgoraResponseVo<T> response = new AgoraResponseVo<>(AgoraMsgCode.SUCCESS);
        response.setData(data);
        return response;
    }

    /**
     * 成功
     *
     * @param <T> 类型参数
     * @return
     */
    public static <T> AgoraResponseVo<T> success() {
        return new AgoraResponseVo<>(AgoraMsgCode.SUCCESS);
    }

    public static <T> AgoraResponseVo<T> msgCode(AgoraMsgCode msgCode) {
        return new AgoraResponseVo<>(msgCode);
    }

    public static AgoraResponseVo<Void> msgCode(int code, String msg) {
        return new AgoraResponseVo<>(code, msg);
    }

    public static AgoraResponseVo<Void> error() {
        return new AgoraResponseVo<>(AgoraMsgCode.ERROR);
    }

    @JSONField(serialize = false)
    public boolean isSuccess() {
        return MsgCode.SUCCESS.getCode() == this.code;
    }

    @Override
    public String toString() {
        return "{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}