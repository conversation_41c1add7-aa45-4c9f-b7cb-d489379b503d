package fm.lizhi.ocean.seal.agora.vo;

import java.util.HashMap;
import java.util.Map;

/**
 * Created in 2022-05-05 15:31.
 *
 * <AUTHOR>
 */
public enum AgoraMsgCode {
    //
    SUCCESS(0, "成功"),
    ERROR_TOKEN(1, "token已失效"),
    ERROR_PARAMS(2, "参数错误"),
    ERROR_MSG_TYPE(3, "消息类型不存在"),
    ERROR_USER_COUNT(4, "游戏人数不符合要求"),

    ERROR_EXCEPTION(400, "操作失败"),
    ERROR(500, "未知错误");
    private int code;
    private String msg;

    private static Map<Integer, AgoraMsgCode> map = new HashMap<>();

    static {
        for (AgoraMsgCode object : AgoraMsgCode.values()) {
            map.put(object.getCode(), object);
        }
    }

    AgoraMsgCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据枚举代码找枚举
     *
     * @param code 值
     * @return
     */
    public static AgoraMsgCode from(int code) {
        return map.get(code);
    }
}