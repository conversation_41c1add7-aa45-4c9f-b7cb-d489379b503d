# LUK刷新用户令牌接口使用示例

## 接口信息
- **URL**: `POST /luk/{appId}/refresh_channel_token`
- **描述**: 该接口用于刷新用户令牌的Token

## 请求参数

### Path参数
- `appId`: seal分给业务的ID

### Body参数 (JSON格式)
```json
{
    "c_id": 1000000,
    "c_uid": "my_uid", 
    "token": "token",
    "timestamp": 1630000000,
    "sign": "sign",
    "left_time": 7200
}
```

| 参数名 | 类型 | 是否必须 | 示例值 | 说明 |
|--------|------|----------|--------|------|
| c_id | integer | 是 | 1000000 | APP ID |
| c_uid | string | 是 | my_uid | 用户 ID |
| token | string | 是 | token | 用户 Token |
| timestamp | integer | 是 | 1630000000 | 秒级时间戳 |
| sign | string | 是 | sign | 签名 |
| left_time | integer | 是 | 7200 | 过期剩余秒数 |

## 返回格式

### 成功响应
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "token": "token",
        "left_time": 7200
    }
}
```

### 失败响应
```json
{
    "code": 1,
    "msg": "失败"
}
```

## 使用示例

### cURL示例
```bash
curl -X POST "http://localhost:8080/luk/your_app_id/refresh_channel_token" \
  -H "Content-Type: application/json" \
  -d '{
    "c_id": 1000000,
    "c_uid": "my_uid",
    "token": "current_token",
    "timestamp": 1630000000,
    "sign": "calculated_sign",
    "left_time": 7200
  }'
```

### Java示例
```java
// 构建请求参数
RefreshChannelTokenReq request = RefreshChannelTokenReq.builder()
    .c_id(1000000)
    .c_uid("my_uid")
    .token("current_token")
    .timestamp(1630000000)
    .sign("calculated_sign")
    .left_time(7200)
    .build();

// 调用接口
LukBaseResp<RefreshChannelTokenResp> response = lukCallBackService.refreshChannelToken(
    request.getC_id(),
    request.getC_uid(),
    request.getToken(),
    request.getTimestamp(),
    request.getSign(),
    request.getLeft_time(),
    "your_app_id"
);

// 处理响应
if (response.getCode() == 0) {
    RefreshChannelTokenResp data = response.getData();
    String newToken = data.getToken();
    Integer leftTime = data.getLeft_time();
    System.out.println("新令牌: " + newToken + ", 剩余时间: " + leftTime + "秒");
} else {
    System.out.println("刷新失败: " + response.getMsg());
}
```

## 注意事项

1. 接口路径中的 `{appId}` 需要替换为实际的应用ID
2. 请求体中的所有字段都是必须的
3. `timestamp` 字段使用秒级时间戳
4. `sign` 字段需要根据业务规则计算签名
5. 返回的 `left_time` 表示新令牌的剩余有效时间（秒）
